import 'dart:convert';
import 'dart:io';
import 'package:get/get.dart';
import 'package:novel_app/utils/network_client.dart';
import 'package:novel_app/controllers/auth_controller.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;

class AIAgentNovelService extends GetxService {
  late final http.Client _httpClient;
  final AuthController _authController = Get.find<AuthController>();

  static const String _baseUrl = 'http://localhost:8000/api/ai-agents';

  @override
  void onInit() {
    super.onInit();
    _httpClient = NetworkClient.createSystemClient();
  }

  @override
  void onClose() {
    _httpClient.close();
    super.onClose();
  }
  
  /// 开始生成小说
  Future<Map<String, dynamic>> startGeneration({
    required String prompt,
    required int targetChapters,
    required String genre,
    String provider = 'openai',
    String? apiKey,
    String? baseUrl,
    String model = 'gpt-4-turbo-preview',
    double temperature = 0.7,
    int maxTokens = 4000,
    String? openaiApiKey, // 向后兼容
  }) async {
    try {
      final response = await _httpClient.post(
        Uri.parse('$_baseUrl/generate-novel'),
        headers: _getAuthHeaders(),
        body: jsonEncode({
          'prompt': prompt,
          'target_chapters': targetChapters,
          'genre': genre,
          'provider': provider,
          'api_key': apiKey ?? openaiApiKey, // 向后兼容
          'base_url': baseUrl,
          'model': model,
          'temperature': temperature,
          'max_tokens': maxTokens,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'task_id': data['task_id'],
          'status': data['status'],
          'message': data['message'],
        };
      } else {
        final data = jsonDecode(response.body);
        return {
          'success': false,
          'message': data['detail'] ?? '启动生成失败',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '网络请求失败: $e',
      };
    }
  }
  
  /// 获取生成状态
  Future<Map<String, dynamic>> getGenerationStatus(String taskId) async {
    try {
      final response = await _httpClient.get(
        Uri.parse('$_baseUrl/generation-status/$taskId'),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'task_id': data['task_id'],
          'status': data['status'],
          'progress': data['progress'],
          'result': data['result'],
          'error': data['error'],
        };
      } else {
        final data = jsonDecode(response.body);
        return {
          'success': false,
          'message': data['detail'] ?? '获取状态失败',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '网络请求失败: $e',
      };
    }
  }
  
  /// 获取生成结果
  Future<Map<String, dynamic>> getGenerationResult(String taskId) async {
    try {
      final response = await _httpClient.get(
        Uri.parse('$_baseUrl/download-novel/$taskId'),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'novel_content': data['novel_content'],
          'metadata': data['metadata'],
          'word_count': data['word_count'],
          'completed_chapters': data['metadata']?['chapters'] ?? 0,
          'target_chapters': data['metadata']?['chapters'] ?? 0,
          'world_bible': data['metadata']?['world_bible'],
          'characters': data['metadata']?['characters'],
          'outline': data['metadata']?['outline'],
          'generated_at': data['metadata']?['generated_at'],
          'chapters': _parseChapters(data['novel_content']),
        };
      } else {
        final data = jsonDecode(response.body);
        return {
          'success': false,
          'error': data['detail'] ?? '获取结果失败',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': '网络请求失败: $e',
      };
    }
  }
  
  /// 取消生成任务
  Future<Map<String, dynamic>> cancelGeneration(String taskId) async {
    try {
      final response = await _httpClient.delete(
        Uri.parse('$_baseUrl/cancel-task/$taskId'),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'message': data['message'],
        };
      } else {
        final data = jsonDecode(response.body);
        return {
          'success': false,
          'message': data['detail'] ?? '取消失败',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '网络请求失败: $e',
      };
    }
  }

  /// 获取用户任务列表
  Future<Map<String, dynamic>> getUserTasks() async {
    try {
      final response = await _httpClient.get(
        Uri.parse('$_baseUrl/user-tasks'),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'tasks': data['tasks'],
        };
      } else {
        final data = jsonDecode(response.body);
        return {
          'success': false,
          'message': data['detail'] ?? '获取任务列表失败',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '网络请求失败: $e',
      };
    }
  }
  
  /// 下载小说到本地
  Future<Map<String, dynamic>> downloadNovel(String taskId) async {
    try {
      // 先获取小说内容
      final result = await getGenerationResult(taskId);
      
      if (!result['success']) {
        return result;
      }
      
      // 保存到本地文件
      final novelContent = result['novel_content'] as String;
      final metadata = result['metadata'] as Map<String, dynamic>?;
      final title = metadata?['title'] ?? 'AI生成小说_$taskId';
      
      // 获取文档目录
      final directory = await getApplicationDocumentsDirectory();
      final fileName = '${title.replaceAll(RegExp(r'[^\w\s-]'), '')}.txt';
      final file = File('${directory.path}/$fileName');
      
      // 写入文件
      await file.writeAsString(novelContent, encoding: utf8);
      
      return {
        'success': true,
        'title': title,
        'file_path': file.path,
        'word_count': result['word_count'],
        'chapters': result['completed_chapters'],
      };
    } catch (e) {
      return {
        'success': false,
        'error': '保存文件失败: $e',
      };
    }
  }
  
  /// 获取系统状态
  Future<Map<String, dynamic>> getSystemStatus() async {
    try {
      final response = await _httpClient.get(
        Uri.parse('$_baseUrl/system-status'),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'system_initialized': data['system_initialized'],
          'active_tasks': data['active_tasks'],
          'total_tasks': data['total_tasks'],
          'openai_configured': data['openai_configured'],
        };
      } else {
        final data = jsonDecode(response.body);
        return {
          'success': false,
          'message': data['detail'] ?? '获取系统状态失败',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '网络请求失败: $e',
      };
    }
  }

  /// 测试智能体系统
  Future<Map<String, dynamic>> testAgents() async {
    try {
      final response = await _httpClient.post(
        Uri.parse('$_baseUrl/test-agents'),
        headers: _getAuthHeaders(),
        body: jsonEncode({}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'message': data['message'],
          'agents': data['agents'],
        };
      } else {
        final data = jsonDecode(response.body);
        return {
          'success': false,
          'message': data['detail'] ?? '测试失败',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '网络请求失败: $e',
      };
    }
  }

  /// 获取可用的模型提供商
  Future<Map<String, dynamic>> getModelProviders() async {
    try {
      final response = await _httpClient.get(
        Uri.parse('$_baseUrl/model-providers'),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'providers': data['providers'],
        };
      } else {
        final data = jsonDecode(response.body);
        return {
          'success': false,
          'message': data['detail'] ?? '获取提供商失败',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '网络请求失败: $e',
      };
    }
  }

  /// 获取指定提供商的模型列表
  Future<Map<String, dynamic>> getProviderModels(String provider) async {
    try {
      final response = await _httpClient.get(
        Uri.parse('$_baseUrl/model-providers/$provider/models'),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return {
          'success': true,
          'provider': data['provider'],
          'models': data['models'],
        };
      } else {
        final data = jsonDecode(response.body);
        return {
          'success': false,
          'message': data['detail'] ?? '获取模型列表失败',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '网络请求失败: $e',
      };
    }
  }
  
  /// 获取认证头
  Map<String, String> _getAuthHeaders() {
    return _authController.authHeaders;
  }
  
  /// 解析章节内容
  List<Map<String, dynamic>> _parseChapters(String novelContent) {
    final chapters = <Map<String, dynamic>>[];
    
    // 简单的章节分割逻辑
    final chapterPattern = RegExp(r'第(\d+)章');
    final matches = chapterPattern.allMatches(novelContent);
    
    for (int i = 0; i < matches.length; i++) {
      final match = matches.elementAt(i);
      final chapterNumber = int.parse(match.group(1)!);
      
      // 获取章节内容
      final startIndex = match.start;
      final endIndex = i < matches.length - 1 
          ? matches.elementAt(i + 1).start 
          : novelContent.length;
      
      final chapterContent = novelContent.substring(startIndex, endIndex).trim();
      
      chapters.add({
        'chapter': chapterNumber,
        'content': chapterContent,
        'word_count': chapterContent.length,
      });
    }
    
    return chapters;
  }
}
