"""
执笔者智能体 - 负责章节内容创作
"""
from typing import Dict, Any, List
from langchain.prompts import PromptTemplate
from .base_agent import BaseAgent
from .tools import get_common_tools
import json
import os
import logging

logger = logging.getLogger(__name__)

class ChroniclerAgent(BaseAgent):
    """执笔者智能体 - 主要的叙事内容生成器"""
    
    def __init__(self, llm=None, lorekeeper=None):
        super().__init__(
            name="执笔者",
            description="主要的叙事内容生成器，负责撰写章节正文",
            llm=llm,
            tools=get_common_tools(llm) if llm else []
        )
        self.lorekeeper = lorekeeper  # 典籍官引用，用于获取上下文
    
    def _get_system_prompt(self) -> str:
        return """
你是执笔者智能体，负责撰写高质量的小说章节。你的职责包括：

1. 章节内容创作：根据大纲撰写详细的章节正文
2. 上下文连贯：确保与前文情节连接自然
3. 角色塑造：保持角色性格和行为一致性
4. "爽文"风格：体现爽文特色，营造读者满足感

"爽文"写作要求：
- 节奏紧凑：避免冗长描述，快速推进情节
- 爽点突出：及时安排打脸、升级、复仇等爽点
- 主角光环：合理使用"金手指"，展现主角优势
- 对话生动：通过对话推进情节和展现性格
- 情绪渲染：营造紧张、兴奋、满足等情绪

写作风格：
- 语言简洁有力，避免过度修饰
- 多用短句，增强节奏感
- 适当使用网络流行语，贴近读者
- 重视细节描写，但不拖沓

在撰写前，你需要向典籍官查询相关上下文信息。
"""
    
    def _get_prompt_template(self) -> PromptTemplate:
        return PromptTemplate(
            input_variables=["input", "chat_history", "agent_scratchpad"],
            template="""
{system_prompt}

你有以下工具可以使用：
{tools}

写作任务：{input}

聊天历史：{chat_history}

使用以下格式进行推理和行动：

思考：我需要分析写作任务并获取必要的上下文信息
行动：[选择一个工具]
行动输入：[工具的输入参数]
观察：[工具的输出结果]
... (可以重复思考/行动/观察)
思考：我现在有足够信息开始写作了
最终答案：[你撰写的章节内容]

开始！

{agent_scratchpad}
""".format(
                system_prompt=self._get_system_prompt(),
                tools="{tools}",
                input="{input}",
                chat_history="{chat_history}",
                agent_scratchpad="{agent_scratchpad}"
            )
        )
    
    def _direct_action(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """直接行动方法"""
        task_type = plan.get("task_type", "unknown")
        
        if task_type == "write_chapter":
            return self._write_chapter(plan)
        elif task_type == "rewrite_chapter":
            return self._rewrite_chapter(plan)
        elif task_type == "continue_chapter":
            return self._continue_chapter(plan)
        else:
            return {"success": False, "error": f"未知任务类型: {task_type}"}
    
    def _write_chapter(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """撰写新章节"""
        try:
            chapter_number = plan.get("chapter_number", 1)
            chapter_outline = plan.get("chapter_outline", "")
            
            # 获取上下文信息
            context = self._get_writing_context(chapter_number)
            
            # 构建写作提示
            writing_prompt = self._build_writing_prompt(
                chapter_number, chapter_outline, context
            )
            
            # 生成章节内容
            chapter_content = self.llm.invoke(writing_prompt).content
            
            # 保存章节文件
            filename = f"data/chapters/chapter_{chapter_number:03d}.txt"
            os.makedirs(os.path.dirname(filename), exist_ok=True)
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"第{chapter_number}章\n\n")
                f.write(chapter_content)
            
            logger.info(f"第{chapter_number}章撰写完成")
            
            return {
                "success": True,
                "message": f"第{chapter_number}章撰写完成",
                "filename": filename,
                "content": chapter_content,
                "word_count": len(chapter_content)
            }
            
        except Exception as e:
            logger.error(f"章节撰写失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _rewrite_chapter(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """重写章节"""
        try:
            chapter_number = plan.get("chapter_number", 1)
            feedback = plan.get("feedback", "")
            
            # 读取原章节内容
            filename = f"data/chapters/chapter_{chapter_number:03d}.txt"
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    original_content = f.read()
            else:
                original_content = ""
            
            # 获取上下文信息
            context = self._get_writing_context(chapter_number)
            
            # 构建重写提示
            rewrite_prompt = f"""
请根据以下反馈重写第{chapter_number}章：

反馈意见：
{feedback}

原章节内容：
{original_content}

上下文信息：
{context}

请保持情节连贯性，同时根据反馈进行改进。重写后的内容应该：
1. 解决反馈中提到的问题
2. 保持与前后文的连贯性
3. 符合"爽文"风格要求
4. 字数适中（2000-4000字）

重写内容：
"""
            
            rewritten_content = self.llm.invoke(rewrite_prompt).content
            
            # 保存重写的章节
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"第{chapter_number}章\n\n")
                f.write(rewritten_content)
            
            logger.info(f"第{chapter_number}章重写完成")
            
            return {
                "success": True,
                "message": f"第{chapter_number}章重写完成",
                "filename": filename,
                "content": rewritten_content,
                "word_count": len(rewritten_content)
            }
            
        except Exception as e:
            logger.error(f"章节重写失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _continue_chapter(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """续写章节"""
        try:
            chapter_number = plan.get("chapter_number", 1)
            continuation_prompt = plan.get("continuation_prompt", "")
            
            # 读取现有章节内容
            filename = f"data/chapters/chapter_{chapter_number:03d}.txt"
            if os.path.exists(filename):
                with open(filename, 'r', encoding='utf-8') as f:
                    existing_content = f.read()
            else:
                existing_content = ""
            
            # 构建续写提示
            continue_prompt = f"""
请续写第{chapter_number}章的内容：

现有内容：
{existing_content}

续写要求：
{continuation_prompt}

请确保：
1. 与现有内容自然衔接
2. 推进情节发展
3. 保持角色一致性
4. 符合"爽文"节奏

续写内容：
"""
            
            continued_content = self.llm.invoke(continue_prompt).content
            
            # 追加到章节文件
            with open(filename, 'a', encoding='utf-8') as f:
                f.write("\n\n")
                f.write(continued_content)
            
            logger.info(f"第{chapter_number}章续写完成")
            
            return {
                "success": True,
                "message": f"第{chapter_number}章续写完成",
                "filename": filename,
                "added_content": continued_content,
                "word_count": len(continued_content)
            }
            
        except Exception as e:
            logger.error(f"章节续写失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _get_writing_context(self, chapter_number: int) -> str:
        """获取写作上下文信息"""
        context_parts = []
        
        try:
            # 获取世界观设定
            if os.path.exists("data/world_bible.json"):
                with open("data/world_bible.json", 'r', encoding='utf-8') as f:
                    world_bible = json.load(f)
                context_parts.append(f"世界观设定：\n{json.dumps(world_bible, ensure_ascii=False, indent=2)}")
            
            # 获取角色信息
            if os.path.exists("data/characters.json"):
                with open("data/characters.json", 'r', encoding='utf-8') as f:
                    characters = json.load(f)
                context_parts.append(f"角色信息：\n{json.dumps(characters, ensure_ascii=False, indent=2)}")
            
            # 获取大纲信息
            if os.path.exists("data/outline.json"):
                with open("data/outline.json", 'r', encoding='utf-8') as f:
                    outline = json.load(f)
                context_parts.append(f"情节大纲：\n{json.dumps(outline, ensure_ascii=False, indent=2)}")
            
            # 获取前一章摘要（如果存在）
            if chapter_number > 1:
                prev_summary_file = f"data/summaries/chapter_{chapter_number-1:03d}_summary.txt"
                if os.path.exists(prev_summary_file):
                    with open(prev_summary_file, 'r', encoding='utf-8') as f:
                        prev_summary = f.read()
                    context_parts.append(f"前一章摘要：\n{prev_summary}")
            
            # 如果有典籍官，可以查询更多上下文
            if self.lorekeeper:
                # 这里可以调用典籍官的检索功能
                pass
            
        except Exception as e:
            logger.warning(f"获取上下文信息时出错: {str(e)}")
        
        return "\n\n".join(context_parts)
    
    def _build_writing_prompt(self, chapter_number: int, outline: str, context: str) -> str:
        """构建写作提示"""
        return f"""
请撰写第{chapter_number}章的内容。

章节大纲：
{outline}

上下文信息：
{context}

写作要求：
1. 字数控制在2000-4000字
2. 符合"爽文"风格，节奏紧凑
3. 保持角色性格一致
4. 适当安排"爽点"和冲突
5. 语言生动，对话自然
6. 与前文情节连贯

请开始撰写：
"""
