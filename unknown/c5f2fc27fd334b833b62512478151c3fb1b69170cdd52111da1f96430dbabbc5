# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\element\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\project\\vs code\\novel\\novel_app" PROJECT_DIR)

set(FLUTTER_VERSION "4.2.15" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 4 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 2 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 15 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 0 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\element\\flutter"
  "PROJECT_DIR=D:\\project\\vs code\\novel\\novel_app"
  "FLUTTER_ROOT=D:\\element\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\project\\vs code\\novel\\novel_app\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\project\\vs code\\novel\\novel_app"
  "FLUTTER_TARGET=D:\\project\\vs code\\novel\\novel_app\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\project\\vs code\\novel\\novel_app\\.dart_tool\\package_config.json"
)
