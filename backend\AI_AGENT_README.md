# 岱宗AI自主创作智能体系统

基于文档《岱宗AI自主创作智能体.md》实现的多智能体小说创作系统，能够自主生成百万字级"爽文"小说。

## 系统架构

### 核心智能体

1. **编排者 (Orchestrator Agent)**
   - 系统的项目经理
   - 协调整个创作流程
   - 监控进度和处理异常

2. **架构师 (Architect Agent)**
   - 世界观构建与大纲规划
   - 创建世界设定、角色档案、情节大纲
   - 实现"爽文"创作范式

3. **执笔者 (Chronicler Agent)**
   - 章节内容创作
   - 根据大纲撰写详细正文
   - 保持上下文连贯性

4. **典籍官 (Lorekeeper Agent)**
   - 分层RAG记忆系统
   - 知识库管理和检索服务
   - 维护创作一致性

5. **校订者 (Editor Agent)**
   - 质量保证和一致性检查
   - 自我批判机制
   - 提供修改建议

### 技术特性

- **分层RAG系统**：四层知识库结构
- **ReAct框架**：推理-行动循环
- **LangGraph工作流**：状态管理和条件路由
- **自我批判机制**：质量自动检查
- **"爽文"范式编码**：类型化创作规则

## 安装和配置

### 1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 环境配置

创建 `.env` 文件：

```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here

# 智能体系统配置
AGENT_OPENAI_MODEL=gpt-4-turbo-preview
AGENT_OPENAI_TEMPERATURE=0.7
AGENT_MAX_ITERATIONS=1000
AGENT_SHUANG_POINT_INTERVAL=7
```

### 3. 创建数据目录

```bash
mkdir -p data/chapters data/summaries data/vectorstore
```

## 使用方法

### 命令行测试

```bash
cd backend
python test_novel_generation.py --prompt "一部修仙小说，主角的金手指是一个可以调试物理法则的系统" --chapters 10
```

### API接口使用

启动后端服务：

```bash
cd backend
python run.py
```

#### 1. 开始生成小说

```http
POST /api/ai-agents/generate-novel
Content-Type: application/json
Authorization: Bearer <your_token>

{
    "prompt": "一部修仙小说，主角的金手指是一个可以调试物理法则的系统",
    "target_chapters": 100,
    "genre": "修仙",
    "model": "gpt-4-turbo-preview"
}
```

响应：
```json
{
    "task_id": "novel_1_1703123456",
    "status": "pending",
    "message": "小说生成任务已启动，请使用task_id查询进度"
}
```

#### 2. 查询生成进度

```http
GET /api/ai-agents/generation-status/{task_id}
Authorization: Bearer <your_token>
```

响应：
```json
{
    "task_id": "novel_1_1703123456",
    "status": "running",
    "progress": {
        "current_chapter": 5,
        "target_chapters": 100,
        "current_phase": "generation"
    },
    "result": null,
    "error": null
}
```

#### 3. 下载生成的小说

```http
GET /api/ai-agents/download-novel/{task_id}
Authorization: Bearer <your_token>
```

### 生成的文件结构

```
data/
├── world_bible.json      # 世界观设定
├── characters.json       # 角色档案
├── outline.json         # 情节大纲
├── chapters/            # 章节文件
│   ├── chapter_001.txt
│   ├── chapter_002.txt
│   └── ...
├── summaries/           # 章节摘要
│   ├── chapter_001_summary.txt
│   └── ...
└── vectorstore/         # 向量数据库
    ├── settings/
    ├── summaries/
    ├── chapters/
    └── entities/
```

## "爽文"创作规则

系统内置了完整的"爽文"创作规则：

### 黄金三章
- **第一章**：主角困境展示，暗示潜力
- **第二章**：冲突引入，"金手指"铺垫
- **第三章**："金手指"初现，首次"爽点"

### "爽点"类型
- 打脸装逼
- 能力突破
- 复仇雪恨
- 获得宝物
- 收服强者
- 震惊众人
- 逆转局势
- 惩恶扬善

### 角色原型
- **反派**：傲慢少爷、嫉妒同辈、势利长辈等
- **配角**：忠诚伙伴、智慧导师、红颜知己等

## 系统监控

### 获取系统状态

```http
GET /api/ai-agents/system-status
```

### 测试智能体

```http
POST /api/ai-agents/test-agents
Authorization: Bearer <your_token>
```

### 获取用户任务

```http
GET /api/ai-agents/user-tasks
Authorization: Bearer <your_token>
```

## 配置说明

### 智能体配置 (agent_config.py)

- `openai_model`: 使用的GPT模型
- `shuang_point_interval`: "爽点"间隔章节数
- `chunk_size`: RAG文本分块大小
- `max_iterations`: 最大迭代次数

### 提示模板

系统包含专业的提示模板：
- 世界观构建模板
- 角色创建模板
- 情节规划模板

## 性能优化

### 成本控制
- 简单任务使用较便宜的模型
- 复杂推理使用高级模型
- 缓存重复查询结果

### 并发处理
- 异步任务执行
- 多任务并行处理
- 状态持久化

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `.env` 文件中的 `OPENAI_API_KEY`
   - 确认API密钥有效且有足够额度

2. **生成失败**
   - 查看错误日志
   - 检查网络连接
   - 验证提示内容是否合规

3. **内存不足**
   - 减少 `target_chapters` 数量
   - 调整 `chunk_size` 参数
   - 清理旧的向量数据库

### 日志查看

```bash
tail -f backend/logs/agent_system.log
```

## 扩展开发

### 添加新智能体

1. 继承 `BaseAgent` 类
2. 实现必要的方法
3. 在工作流中添加节点
4. 配置路由规则

### 自定义创作规则

修改 `agent_config.py` 中的 `SHUANG_WEN_RULES` 配置。

### 集成新的LLM

在 `NovelGenerationSystem` 中替换 `ChatOpenAI` 实例。

## 许可证

本项目基于MIT许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进系统。

## 联系方式

如有问题，请通过GitHub Issues联系。
