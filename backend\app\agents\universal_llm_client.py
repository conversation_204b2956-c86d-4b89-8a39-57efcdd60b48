"""
通用LLM客户端 - 支持多种兼容OpenAI格式的模型
"""
import os
import logging
from typing import Dict, Any, Optional, List
from langchain_openai import ChatOpenAI
from langchain_community.chat_models import ChatOllama
from langchain.schema import BaseMessage, HumanMessage, SystemMessage, AIMessage

logger = logging.getLogger(__name__)

class UniversalLLMClient:
    """通用LLM客户端，支持多种模型提供商"""
    
    def __init__(
        self,
        provider: str = "openai",
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        model_name: str = "gpt-3.5-turbo",
        temperature: float = 0.7,
        max_tokens: int = 4000,
        api_version: Optional[str] = None,
        organization: Optional[str] = None,
        **kwargs
    ):
        self.provider = provider
        self.api_key = api_key
        self.base_url = base_url
        self.model_name = model_name
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.api_version = api_version
        self.organization = organization
        self.kwargs = kwargs
        
        self.client = self._create_client()
    
    def _create_client(self):
        """根据提供商创建相应的客户端"""
        try:
            if self.provider == "ollama":
                return self._create_ollama_client()
            else:
                return self._create_openai_compatible_client()
        except Exception as e:
            logger.error(f"创建LLM客户端失败: {str(e)}")
            raise
    
    def _create_openai_compatible_client(self):
        """创建OpenAI兼容的客户端"""
        # 设置环境变量（如果提供了的话）
        if self.api_key:
            os.environ["OPENAI_API_KEY"] = self.api_key
        if self.organization:
            os.environ["OPENAI_ORG_ID"] = self.organization
        
        # 构建客户端参数
        client_kwargs = {
            "model": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
        }
        
        # 添加base_url（用于自定义API端点）
        if self.base_url:
            client_kwargs["openai_api_base"] = self.base_url
        
        # 添加API版本（用于Azure OpenAI）
        if self.api_version:
            client_kwargs["openai_api_version"] = self.api_version
        
        # 添加其他参数
        client_kwargs.update(self.kwargs)
        
        return ChatOpenAI(**client_kwargs)
    
    def _create_ollama_client(self):
        """创建Ollama客户端"""
        base_url = self.base_url or "http://localhost:11434"
        
        return ChatOllama(
            model=self.model_name,
            base_url=base_url,
            temperature=self.temperature,
            **self.kwargs
        )
    
    def invoke(self, messages: List[BaseMessage]) -> str:
        """调用模型生成回复"""
        try:
            if isinstance(messages, str):
                # 如果输入是字符串，转换为消息格式
                messages = [HumanMessage(content=messages)]
            elif isinstance(messages, list) and len(messages) > 0 and isinstance(messages[0], str):
                # 如果输入是字符串列表，转换为消息格式
                messages = [HumanMessage(content=msg) for msg in messages]
            
            response = self.client.invoke(messages)
            return response.content
        except Exception as e:
            logger.error(f"LLM调用失败: {str(e)}")
            raise
    
    def invoke_with_prompt(self, prompt: str, system_prompt: Optional[str] = None) -> str:
        """使用提示词调用模型"""
        messages = []
        
        if system_prompt:
            messages.append(SystemMessage(content=system_prompt))
        
        messages.append(HumanMessage(content=prompt))
        
        return self.invoke(messages)
    
    def stream(self, messages: List[BaseMessage]):
        """流式调用模型"""
        try:
            if isinstance(messages, str):
                messages = [HumanMessage(content=messages)]
            
            for chunk in self.client.stream(messages):
                yield chunk.content
        except Exception as e:
            logger.error(f"LLM流式调用失败: {str(e)}")
            raise
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "provider": self.provider,
            "model_name": self.model_name,
            "base_url": self.base_url,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
        }
    
    @classmethod
    def from_config(cls, config: Dict[str, Any]) -> "UniversalLLMClient":
        """从配置创建客户端"""
        return cls(
            provider=config.get("provider", "openai"),
            api_key=config.get("api_key"),
            base_url=config.get("base_url"),
            model_name=config.get("model_name", "gpt-3.5-turbo"),
            temperature=config.get("temperature", 0.7),
            max_tokens=config.get("max_tokens", 4000),
            api_version=config.get("api_version"),
            organization=config.get("organization"),
        )
    
    @classmethod
    def create_openai_client(cls, api_key: str, model: str = "gpt-4-turbo-preview") -> "UniversalLLMClient":
        """快速创建OpenAI客户端"""
        return cls(
            provider="openai",
            api_key=api_key,
            model_name=model,
        )
    
    @classmethod
    def create_ollama_client(cls, model: str = "llama2", base_url: str = "http://localhost:11434") -> "UniversalLLMClient":
        """快速创建Ollama客户端"""
        return cls(
            provider="ollama",
            model_name=model,
            base_url=base_url,
        )
    
    @classmethod
    def create_custom_client(
        cls, 
        base_url: str, 
        model: str, 
        api_key: Optional[str] = None
    ) -> "UniversalLLMClient":
        """快速创建自定义API客户端"""
        return cls(
            provider="custom",
            api_key=api_key,
            base_url=base_url,
            model_name=model,
        )

def create_llm_from_env() -> UniversalLLMClient:
    """从环境变量创建LLM客户端"""
    config = {
        "provider": os.getenv("LLM_PROVIDER", "openai"),
        "api_key": os.getenv("LLM_API_KEY") or os.getenv("OPENAI_API_KEY"),
        "base_url": os.getenv("LLM_BASE_URL"),
        "model_name": os.getenv("LLM_MODEL", "gpt-3.5-turbo"),
        "temperature": float(os.getenv("LLM_TEMPERATURE", "0.7")),
        "max_tokens": int(os.getenv("LLM_MAX_TOKENS", "4000")),
        "api_version": os.getenv("LLM_API_VERSION"),
        "organization": os.getenv("LLM_ORGANIZATION"),
    }
    
    return UniversalLLMClient.from_config(config)

# 预定义的客户端工厂函数
def create_deepseek_client(api_key: str) -> UniversalLLMClient:
    """创建DeepSeek客户端"""
    return UniversalLLMClient(
        provider="deepseek",
        api_key=api_key,
        base_url="https://api.deepseek.com/v1",
        model_name="deepseek-chat",
    )

def create_moonshot_client(api_key: str) -> UniversalLLMClient:
    """创建月之暗面客户端"""
    return UniversalLLMClient(
        provider="moonshot",
        api_key=api_key,
        base_url="https://api.moonshot.cn/v1",
        model_name="moonshot-v1-8k",
    )

def create_zhipu_client(api_key: str) -> UniversalLLMClient:
    """创建智谱GLM客户端"""
    return UniversalLLMClient(
        provider="zhipu",
        api_key=api_key,
        base_url="https://open.bigmodel.cn/api/paas/v4",
        model_name="glm-4",
    )

def create_local_client(base_url: str, model: str = "local-model") -> UniversalLLMClient:
    """创建本地模型客户端"""
    return UniversalLLMClient(
        provider="local",
        base_url=base_url,
        model_name=model,
        api_key="dummy",  # 某些本地服务可能需要非空的API密钥
    )
