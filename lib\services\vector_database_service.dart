import 'dart:convert';
import 'dart:math' as math;
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';
import 'package:novel_app/models/memory_chunk.dart';
import 'package:novel_app/services/embedding_service.dart';

/// 向量数据库服务
/// 负责存储和检索记忆块
class VectorDatabaseService extends GetxService {
  static const String _boxName = 'memory_chunks';
  late Box<MemoryChunk> _memoryBox;
  final EmbeddingService _embeddingService = Get.find<EmbeddingService>();
  final Uuid _uuid = const Uuid();

  /// 初始化服务
  Future<VectorDatabaseService> init() async {
    try {
      _memoryBox = await Hive.openBox<MemoryChunk>(_boxName);
      print('[VectorDB] 向量数据库初始化成功，当前记忆块数量: ${_memoryBox.length}');
      return this;
    } catch (e) {
      print('[VectorDB] 向量数据库初始化失败: $e');
      rethrow;
    }
  }

  /// 添加记忆块
  Future<String> addMemoryChunk({
    required String content,
    required MemoryChunkType type,
    MemoryImportance importance = MemoryImportance.normal,
    Map<String, dynamic> metadata = const {},
    String? sourceChapter,
    String? sourceScene,
    List<String> tags = const [],
    String? relatedCharacters,
    String? relatedLocations,
  }) async {
    try {
      // 验证输入
      if (content.trim().isEmpty) {
        throw Exception('记忆块内容不能为空');
      }

      // 生成嵌入向量，添加重试机制
      List<double> embedding = []; // 初始化为空列表
      int retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          embedding = await _embeddingService.generateEmbedding(content);
          break;
        } catch (e) {
          retryCount++;
          print('[VectorDB] 生成嵌入向量失败 (尝试 $retryCount/$maxRetries): $e');

          if (retryCount >= maxRetries) {
            // 如果所有重试都失败，使用零向量作为fallback
            print('[VectorDB] 使用零向量作为fallback');
            embedding = List<double>.filled(384, 0.0); // 默认384维度
            break;
          }

          // 等待一段时间后重试
          await Future.delayed(Duration(seconds: retryCount));
        }
      }

      // 创建记忆块
      final chunk = MemoryChunk(
        id: _uuid.v4(),
        content: content,
        embedding: embedding,
        type: type,
        importance: importance,
        metadata: Map<String, dynamic>.from(metadata), // 创建副本避免引用问题
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        sourceChapter: sourceChapter,
        sourceScene: sourceScene,
        tags: List<String>.from(tags), // 创建副本避免引用问题
        relatedCharacters: relatedCharacters,
        relatedLocations: relatedLocations,
      );

      // 存储到数据库，添加重试机制
      await _safeStoreChunk(chunk);

      print('[VectorDB] 添加记忆块成功: ${chunk.id} (${type.toString()})');
      return chunk.id;
    } catch (e) {
      print('[VectorDB] 添加记忆块失败: $e');
      rethrow;
    }
  }

  /// 安全存储记忆块，包含重试机制
  Future<void> _safeStoreChunk(MemoryChunk chunk) async {
    int retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        await _memoryBox.put(chunk.id, chunk);
        return; // 成功存储，退出循环
      } catch (e) {
        retryCount++;
        print('[VectorDB] 存储记忆块失败 (尝试 $retryCount/$maxRetries): $e');

        if (retryCount >= maxRetries) {
          throw Exception('存储记忆块失败，已重试$maxRetries次: $e');
        }

        // 等待一段时间后重试
        await Future.delayed(Duration(milliseconds: 500 * retryCount));
      }
    }
  }

  /// 批量添加记忆块
  Future<List<String>> addMemoryChunks(List<Map<String, dynamic>> chunks) async {
    final List<String> ids = [];
    
    for (final chunkData in chunks) {
      try {
        final id = await addMemoryChunk(
          content: chunkData['content'],
          type: chunkData['type'] ?? MemoryChunkType.other,
          importance: chunkData['importance'] ?? MemoryImportance.normal,
          metadata: chunkData['metadata'] ?? {},
          sourceChapter: chunkData['sourceChapter'],
          sourceScene: chunkData['sourceScene'],
          tags: List<String>.from(chunkData['tags'] ?? []),
          relatedCharacters: chunkData['relatedCharacters'],
          relatedLocations: chunkData['relatedLocations'],
        );
        ids.add(id);
      } catch (e) {
        print('[VectorDB] 批量添加记忆块失败: $e');
      }
    }
    
    return ids;
  }

  /// 语义搜索
  Future<List<MemoryChunk>> semanticSearch({
    required String query,
    int limit = 10,
    double threshold = 0.5,
    List<MemoryChunkType>? typeFilter,
    List<MemoryImportance>? importanceFilter,
    List<String>? tagFilter,
    String? characterFilter,
    String? locationFilter,
  }) async {
    try {
      // 验证输入
      if (query.trim().isEmpty) {
        print('[VectorDB] 查询字符串为空，返回空结果');
        return [];
      }

      // 生成查询向量，添加重试机制
      List<double> queryEmbedding;
      try {
        queryEmbedding = await _embeddingService.generateEmbedding(query);
      } catch (e) {
        print('[VectorDB] 生成查询向量失败，降级到关键词搜索: $e');
        return await keywordSearch(
          query: query,
          limit: limit,
          typeFilter: typeFilter,
        );
      }

      // 获取所有记忆块
      final allChunks = _memoryBox.values.toList();

      if (allChunks.isEmpty) {
        print('[VectorDB] 记忆库为空，返回空结果');
        return [];
      }

      // 过滤和计算相似度
      final List<MemoryChunk> candidates = [];

      for (final chunk in allChunks) {
        try {
          // 类型过滤
          if (typeFilter != null && !typeFilter.contains(chunk.type)) {
            continue;
          }

          // 重要性过滤
          if (importanceFilter != null && !importanceFilter.contains(chunk.importance)) {
            continue;
          }

          // 标签过滤
          if (tagFilter != null && tagFilter.isNotEmpty) {
            final hasMatchingTag = chunk.tags.any((tag) => tagFilter.contains(tag));
            if (!hasMatchingTag) continue;
          }

          // 人物过滤
          if (characterFilter != null && chunk.relatedCharacters != null) {
            if (!chunk.relatedCharacters!.contains(characterFilter)) {
              continue;
            }
          }

          // 地点过滤
          if (locationFilter != null && chunk.relatedLocations != null) {
            if (!chunk.relatedLocations!.contains(locationFilter)) {
              continue;
            }
          }

          // 检查嵌入向量是否有效
          if (chunk.embedding.isEmpty) {
            print('[VectorDB] 记忆块 ${chunk.id} 的嵌入向量为空，跳过');
            continue;
          }

          // 计算相关性分数
          final relevanceScore = chunk.calculateRelevanceScore(
            queryEmbedding,
            queryTags: tagFilter,
            queryCharacter: characterFilter,
            queryLocation: locationFilter,
          );

          // 阈值过滤
          if (relevanceScore >= threshold) {
            chunk.relevanceScore = relevanceScore;
            candidates.add(chunk);
          }
        } catch (e) {
          print('[VectorDB] 处理记忆块 ${chunk.id} 时出错: $e');
          continue; // 跳过有问题的记忆块
        }
      }

      // 按相关性分数排序
      candidates.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));

      // 返回前N个结果
      final results = candidates.take(limit).toList();

      print('[VectorDB] 语义搜索完成: 查询"$query", 找到${results.length}个相关记忆块');
      return results;
    } catch (e) {
      print('[VectorDB] 语义搜索失败: $e');
      // 降级到关键词搜索
      try {
        return await keywordSearch(
          query: query,
          limit: limit,
          typeFilter: typeFilter,
        );
      } catch (fallbackError) {
        print('[VectorDB] 关键词搜索也失败: $fallbackError');
        return [];
      }
    }
  }

  /// 混合搜索（语义搜索 + 关键词搜索）
  Future<List<MemoryChunk>> hybridSearch({
    required String query,
    int limit = 10,
    double semanticWeight = 0.7,
    double keywordWeight = 0.3,
    double threshold = 0.4,
    List<MemoryChunkType>? typeFilter,
  }) async {
    try {
      // 语义搜索
      final semanticResults = await semanticSearch(
        query: query,
        limit: limit * 2, // 获取更多候选
        threshold: threshold * 0.8,
        typeFilter: typeFilter,
      );
      
      // 关键词搜索
      final keywordResults = await keywordSearch(
        query: query,
        limit: limit * 2,
        typeFilter: typeFilter,
      );
      
      // 合并结果并重新计算分数
      final Map<String, MemoryChunk> combinedResults = {};
      
      // 添加语义搜索结果
      for (final chunk in semanticResults) {
        chunk.relevanceScore = chunk.relevanceScore * semanticWeight;
        combinedResults[chunk.id] = chunk;
      }
      
      // 添加关键词搜索结果
      for (final chunk in keywordResults) {
        if (combinedResults.containsKey(chunk.id)) {
          // 如果已存在，累加分数
          combinedResults[chunk.id]!.relevanceScore += chunk.relevanceScore * keywordWeight;
        } else {
          // 新结果
          chunk.relevanceScore = chunk.relevanceScore * keywordWeight;
          combinedResults[chunk.id] = chunk;
        }
      }
      
      // 排序并返回
      final results = combinedResults.values.toList();
      results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
      
      print('[VectorDB] 混合搜索完成: 查询"$query", 找到${results.take(limit).length}个相关记忆块');
      return results.take(limit).toList();
    } catch (e) {
      print('[VectorDB] 混合搜索失败: $e');
      return [];
    }
  }

  /// 关键词搜索
  Future<List<MemoryChunk>> keywordSearch({
    required String query,
    int limit = 10,
    List<MemoryChunkType>? typeFilter,
  }) async {
    try {
      final keywords = query.toLowerCase().split(' ');
      final allChunks = _memoryBox.values.toList();
      final List<MemoryChunk> results = [];
      
      for (final chunk in allChunks) {
        // 类型过滤
        if (typeFilter != null && !typeFilter.contains(chunk.type)) {
          continue;
        }
        
        final content = chunk.content.toLowerCase();
        int matchCount = 0;
        
        for (final keyword in keywords) {
          if (content.contains(keyword)) {
            matchCount++;
          }
        }
        
        if (matchCount > 0) {
          chunk.relevanceScore = matchCount / keywords.length;
          results.add(chunk);
        }
      }
      
      results.sort((a, b) => b.relevanceScore.compareTo(a.relevanceScore));
      return results.take(limit).toList();
    } catch (e) {
      print('[VectorDB] 关键词搜索失败: $e');
      return [];
    }
  }

  /// 获取记忆块
  MemoryChunk? getMemoryChunk(String id) {
    return _memoryBox.get(id);
  }

  /// 更新记忆块
  Future<bool> updateMemoryChunk(String id, MemoryChunk updatedChunk) async {
    try {
      updatedChunk.updatedAt = DateTime.now();
      await _memoryBox.put(id, updatedChunk);
      print('[VectorDB] 更新记忆块成功: $id');
      return true;
    } catch (e) {
      print('[VectorDB] 更新记忆块失败: $e');
      return false;
    }
  }

  /// 删除记忆块
  Future<bool> deleteMemoryChunk(String id) async {
    try {
      await _memoryBox.delete(id);
      print('[VectorDB] 删除记忆块成功: $id');
      return true;
    } catch (e) {
      print('[VectorDB] 删除记忆块失败: $e');
      return false;
    }
  }

  /// 清空所有记忆块
  Future<void> clearAllMemories() async {
    try {
      await _memoryBox.clear();
      print('[VectorDB] 清空所有记忆块成功');
    } catch (e) {
      print('[VectorDB] 清空记忆块失败: $e');
    }
  }

  /// 获取统计信息
  Map<String, dynamic> getStatistics() {
    try {
      final allChunks = _memoryBox.values.toList();
      final typeCount = <MemoryChunkType, int>{};
      final importanceCount = <MemoryImportance, int>{};
      int invalidChunks = 0;

      for (final chunk in allChunks) {
        try {
          typeCount[chunk.type] = (typeCount[chunk.type] ?? 0) + 1;
          importanceCount[chunk.importance] = (importanceCount[chunk.importance] ?? 0) + 1;

          // 检查记忆块是否有效
          if (chunk.content.isEmpty || chunk.embedding.isEmpty) {
            invalidChunks++;
          }
        } catch (e) {
          invalidChunks++;
          print('[VectorDB] 统计时发现无效记忆块: $e');
        }
      }

      return {
        'totalChunks': allChunks.length,
        'invalidChunks': invalidChunks,
        'typeDistribution': typeCount.map((k, v) => MapEntry(k.toString(), v)),
        'importanceDistribution': importanceCount.map((k, v) => MapEntry(k.toString(), v)),
        'averageContentLength': allChunks.isEmpty ? 0 : allChunks.map((c) => c.content.length).reduce((a, b) => a + b) / allChunks.length,
        'healthScore': allChunks.isEmpty ? 1.0 : (allChunks.length - invalidChunks) / allChunks.length,
      };
    } catch (e) {
      print('[VectorDB] 获取统计信息失败: $e');
      return {
        'totalChunks': 0,
        'invalidChunks': 0,
        'typeDistribution': {},
        'importanceDistribution': {},
        'averageContentLength': 0,
        'healthScore': 0.0,
        'error': e.toString(),
      };
    }
  }

  /// 数据库健康检查和修复
  Future<Map<String, dynamic>> performHealthCheck({bool autoFix = false}) async {
    try {
      print('[VectorDB] 开始数据库健康检查...');

      final allChunks = _memoryBox.values.toList();
      final issues = <String>[];
      final fixedIssues = <String>[];
      final invalidChunkIds = <String>[];

      for (final chunk in allChunks) {
        try {
          // 检查内容是否为空
          if (chunk.content.trim().isEmpty) {
            issues.add('记忆块 ${chunk.id} 内容为空');
            invalidChunkIds.add(chunk.id);
          }

          // 检查嵌入向量是否为空
          if (chunk.embedding.isEmpty) {
            issues.add('记忆块 ${chunk.id} 嵌入向量为空');
            if (autoFix && chunk.content.trim().isNotEmpty) {
              try {
                // 尝试重新生成嵌入向量
                final newEmbedding = await _embeddingService.generateEmbedding(chunk.content);
                chunk.embedding = newEmbedding;
                await _memoryBox.put(chunk.id, chunk);
                fixedIssues.add('重新生成了记忆块 ${chunk.id} 的嵌入向量');
              } catch (e) {
                print('[VectorDB] 修复记忆块 ${chunk.id} 失败: $e');
              }
            }
          }

          // 检查嵌入向量维度是否一致
          if (chunk.embedding.isNotEmpty && chunk.embedding.length < 100) {
            issues.add('记忆块 ${chunk.id} 嵌入向量维度异常: ${chunk.embedding.length}');
          }

          // 检查时间戳是否有效
          if (chunk.createdAt.isAfter(DateTime.now())) {
            issues.add('记忆块 ${chunk.id} 创建时间异常');
          }

        } catch (e) {
          issues.add('记忆块 ${chunk.id} 数据损坏: $e');
          invalidChunkIds.add(chunk.id);
        }
      }

      // 如果启用自动修复，删除无效的记忆块
      if (autoFix && invalidChunkIds.isNotEmpty) {
        for (final id in invalidChunkIds) {
          try {
            await _memoryBox.delete(id);
            fixedIssues.add('删除了无效记忆块 $id');
          } catch (e) {
            print('[VectorDB] 删除无效记忆块 $id 失败: $e');
          }
        }
      }

      final result = {
        'totalChunks': allChunks.length,
        'issuesFound': issues.length,
        'issues': issues,
        'fixedIssues': fixedIssues,
        'invalidChunks': invalidChunkIds.length,
        'healthScore': allChunks.isEmpty ? 1.0 : (allChunks.length - invalidChunkIds.length) / allChunks.length,
        'timestamp': DateTime.now().toIso8601String(),
      };

      print('[VectorDB] 健康检查完成: 发现${issues.length}个问题，修复${fixedIssues.length}个问题');
      return result;

    } catch (e) {
      print('[VectorDB] 健康检查失败: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  @override
  void onClose() {
    _memoryBox.close();
    super.onClose();
  }
}
