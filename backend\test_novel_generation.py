#!/usr/bin/env python3
"""
小说生成系统测试脚本
"""
import os
import sys
import argparse
import logging
from dotenv import load_dotenv

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.agents.novel_generation_system import NovelGenerationSystem

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI小说生成系统测试")
    parser.add_argument("--prompt", required=True, help="创作提示")
    parser.add_argument("--chapters", type=int, default=5, help="目标章节数")
    parser.add_argument("--model", default="gpt-4-turbo-preview", help="使用的模型")
    parser.add_argument("--api-key", help="OpenAI API密钥")
    
    args = parser.parse_args()
    
    # 检查API密钥
    api_key = args.api_key or os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("错误：请设置OPENAI_API_KEY环境变量或使用--api-key参数")
        return 1
    
    try:
        print("=" * 60)
        print("岱宗AI自主创作智能体系统")
        print("=" * 60)
        print(f"创作提示: {args.prompt}")
        print(f"目标章节: {args.chapters}")
        print(f"使用模型: {args.model}")
        print("=" * 60)
        
        # 初始化系统
        print("正在初始化智能体系统...")
        system = NovelGenerationSystem(api_key, args.model)
        print("智能体系统初始化完成！")
        
        # 开始生成
        print("\n开始生成小说...")
        result = system.generate_novel(
            user_prompt=args.prompt,
            target_chapters=args.chapters
        )
        
        # 输出结果
        print("\n" + "=" * 60)
        print("生成结果")
        print("=" * 60)
        
        if result["success"]:
            print(f"✅ 生成成功！")
            print(f"完成章节: {result['completed_chapters']}/{result['target_chapters']}")
            print(f"完成任务: {len(result['completed_tasks'])}")
            
            # 显示世界观
            if result.get("world_bible"):
                print(f"\n📖 世界观设定已创建")
            
            # 显示角色
            if result.get("characters"):
                print(f"👥 角色档案已创建")
            
            # 显示大纲
            if result.get("outline"):
                print(f"📋 情节大纲已创建")
            
            print(f"\n📁 生成的文件保存在 data/ 目录中")
            print(f"   - 世界观: data/world_bible.json")
            print(f"   - 角色: data/characters.json")
            print(f"   - 大纲: data/outline.json")
            print(f"   - 章节: data/chapters/")
            
        else:
            print(f"❌ 生成失败")
            if result.get("error"):
                print(f"错误: {result['error']}")
            if result.get("errors"):
                print("错误列表:")
                for error in result["errors"]:
                    print(f"  - {error}")
        
        print("\n" + "=" * 60)
        return 0 if result["success"] else 1
        
    except KeyboardInterrupt:
        print("\n\n用户中断生成")
        return 1
    except Exception as e:
        print(f"\n❌ 系统错误: {str(e)}")
        return 1

if __name__ == "__main__":
    exit(main())
