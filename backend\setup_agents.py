#!/usr/bin/env python3
"""
AI智能体系统安装和设置脚本
"""
import os
import sys
import subprocess
import json
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 70)
    print("岱宗AI自主创作智能体系统 - 安装向导")
    print("=" * 70)
    print("基于《岱宗AI自主创作智能体.md》文档实现")
    print("支持自主生成百万字级'爽文'小说")
    print("=" * 70)

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    # 基础依赖
    basic_deps = [
        "fastapi",
        "uvicorn",
        "python-dotenv",
        "pydantic",
        "pydantic-settings"
    ]
    
    # AI相关依赖
    ai_deps = [
        "openai>=1.0.0",
        "langchain>=0.1.0",
        "tiktoken>=0.5.0",
        "numpy>=1.24.0"
    ]
    
    # 可选的高级依赖
    advanced_deps = [
        "langgraph>=0.0.40",
        "langchain-openai>=0.0.8",
        "langchain-community>=0.0.20",
        "faiss-cpu>=1.7.4",
        "sentence-transformers>=2.2.0"
    ]
    
    try:
        # 安装基础依赖
        print("安装基础依赖...")
        for dep in basic_deps:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
        
        # 安装AI依赖
        print("安装AI相关依赖...")
        for dep in ai_deps:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            except subprocess.CalledProcessError as e:
                print(f"⚠️  安装 {dep} 失败: {e}")
        
        # 尝试安装高级依赖
        print("尝试安装高级依赖（可能需要较长时间）...")
        for dep in advanced_deps:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
                print(f"✅ 成功安装: {dep}")
            except subprocess.CalledProcessError as e:
                print(f"⚠️  安装 {dep} 失败，将使用简化版本: {e}")
        
        print("✅ 依赖安装完成")
        return True
        
    except Exception as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建目录结构...")
    
    directories = [
        "data",
        "data/chapters",
        "data/summaries", 
        "data/vectorstore",
        "logs"
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {dir_path}")
    
    return True

def create_env_file():
    """创建环境配置文件"""
    print("\n⚙️  配置环境变量...")
    
    env_file = ".env"
    if os.path.exists(env_file):
        print(f"⚠️  {env_file} 已存在，跳过创建")
        return True
    
    env_content = """# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here

# 智能体系统配置
AGENT_OPENAI_MODEL=gpt-4-turbo-preview
AGENT_OPENAI_TEMPERATURE=0.7
AGENT_MAX_ITERATIONS=1000
AGENT_SHUANG_POINT_INTERVAL=7

# 数据库配置（如果需要）
DATABASE_URL=sqlite:///./novel_app.db

# 其他配置
DEBUG=True
"""
    
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        print(f"✅ 创建环境配置文件: {env_file}")
        print("⚠️  请编辑 .env 文件，设置您的 OPENAI_API_KEY")
        return True
    except Exception as e:
        print(f"❌ 创建环境配置文件失败: {e}")
        return False

def create_demo_config():
    """创建演示配置"""
    print("\n🎯 创建演示配置...")
    
    demo_config = {
        "demo_prompts": [
            "一部修仙小说，主角的金手指是一个可以调试物理法则的系统",
            "一部都市异能小说，主角获得了时间回溯的能力",
            "一部玄幻小说，主角拥有吞噬万物进化的天赋",
            "一部科幻小说，主角在末世中觉醒了空间异能"
        ],
        "test_settings": {
            "target_chapters": 5,
            "model": "gpt-4-turbo-preview",
            "temperature": 0.7
        },
        "shuang_wen_examples": {
            "golden_three_chapters": "前三章必须：1.展示困境 2.引入冲突 3.首次爽点",
            "shuang_point_types": ["打脸", "升级", "复仇", "装逼", "收服", "震惊"],
            "character_types": ["傲慢少爷", "嫉妒同辈", "势利长辈", "忠诚伙伴"]
        }
    }
    
    try:
        with open("demo_config.json", 'w', encoding='utf-8') as f:
            json.dump(demo_config, f, ensure_ascii=False, indent=2)
        print("✅ 创建演示配置文件: demo_config.json")
        return True
    except Exception as e:
        print(f"❌ 创建演示配置失败: {e}")
        return False

def test_installation():
    """测试安装"""
    print("\n🧪 测试安装...")
    
    try:
        # 测试基础导入
        import openai
        print("✅ OpenAI库导入成功")
        
        try:
            import langchain
            print("✅ LangChain库导入成功")
        except ImportError:
            print("⚠️  LangChain库导入失败，将使用简化版本")
        
        try:
            import numpy
            print("✅ NumPy库导入成功")
        except ImportError:
            print("⚠️  NumPy库导入失败")
        
        # 测试环境变量
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv("OPENAI_API_KEY")
        if api_key and api_key != "your_openai_api_key_here":
            print("✅ OpenAI API密钥已配置")
        else:
            print("⚠️  OpenAI API密钥未配置，请编辑 .env 文件")
        
        print("✅ 基础测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "=" * 70)
    print("🎉 安装完成！使用说明：")
    print("=" * 70)
    
    print("\n1. 配置API密钥：")
    print("   编辑 .env 文件，设置您的 OPENAI_API_KEY")
    
    print("\n2. 运行简化演示：")
    print("   python demo_simple.py")
    
    print("\n3. 运行完整测试：")
    print("   python test_novel_generation.py --prompt '您的创作提示' --chapters 5")
    
    print("\n4. 启动API服务：")
    print("   python run.py")
    print("   然后访问 http://localhost:8000/docs 查看API文档")
    
    print("\n5. 系统特性：")
    print("   ✨ 多智能体协作：编排者、架构师、执笔者、典籍官、校订者")
    print("   ✨ 分层RAG记忆：四层知识库结构")
    print("   ✨ '爽文'范式：内置爽文创作规则")
    print("   ✨ 自我批判：质量自动检查机制")
    
    print("\n6. 生成的文件：")
    print("   📖 data/world_bible.json - 世界观设定")
    print("   👥 data/characters.json - 角色档案")
    print("   📋 data/outline.json - 情节大纲")
    print("   📚 data/chapters/ - 章节文件")
    
    print("\n7. 获取帮助：")
    print("   查看 AI_AGENT_README.md 获取详细文档")
    
    print("\n" + "=" * 70)

def main():
    """主安装流程"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败，请手动安装")
        return 1
    
    # 创建目录
    if not create_directories():
        return 1
    
    # 创建配置文件
    if not create_env_file():
        return 1
    
    # 创建演示配置
    if not create_demo_config():
        return 1
    
    # 测试安装
    if not test_installation():
        print("⚠️  测试未完全通过，但基础功能应该可用")
    
    # 显示使用说明
    show_usage_instructions()
    
    return 0

if __name__ == "__main__":
    exit(main())
