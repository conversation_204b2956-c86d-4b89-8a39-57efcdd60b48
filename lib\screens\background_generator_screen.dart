import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/services/background_generator_service.dart';
import 'package:novel_app/controllers/genre_controller.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/widgets/common/loading_overlay.dart';
import 'package:novel_app/prompts/genre_prompts.dart';
import 'package:flutter/services.dart';

class BackgroundGeneratorScreen extends StatefulWidget {
  const BackgroundGeneratorScreen({super.key});

  @override
  _BackgroundGeneratorScreenState createState() =>
      _BackgroundGeneratorScreenState();
}

class _BackgroundGeneratorScreenState extends State<BackgroundGeneratorScreen> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _initialIdeaController = TextEditingController();

  final RxString _selectedGenre = ''.obs;
  final RxBool _isDetailed = false.obs;
  final RxBool _isGenerating = false.obs;
  final RxString _generatedBackground = ''.obs;

  final BackgroundGeneratorService _backgroundGeneratorService =
      Get.find<BackgroundGeneratorService>();
  final GenreController _genreController = Get.find<GenreController>();
  final NovelController _novelController = Get.find<NovelController>();

  @override
  void initState() {
    super.initState();
    if (_genreController.genres.isNotEmpty) {
      _selectedGenre.value = _genreController.genres.first.name;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _initialIdeaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('故事背景生成器'),
        actions: [
          Obx(() => IconButton(
            icon: const Icon(Icons.copy),
            tooltip: '复制到剪贴板',
            onPressed: _generatedBackground.value.isEmpty
                ? null
                : () {
                    Clipboard.setData(ClipboardData(text: _generatedBackground.value));
                    Get.snackbar('成功', '背景已复制到剪贴板');
                  },
          )),
        ],
      ),
      body: LoadingOverlay(
        isLoading: _isGenerating.value,
        loadingText: '正在生成背景...',
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildGenerationForm(),
              const SizedBox(height: 16),
              _buildGeneratedContent(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGenerationForm() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '背景生成设置',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: '小说标题',
                border: OutlineInputBorder(),
                hintText: '请输入小说标题',
              ),
            ),
            const SizedBox(height: 16),
            Obx(() {
              // 获取唯一的类型名称列表，避免重复
              final Map<String, NovelGenre> uniqueGenres = <String, NovelGenre>{};
              for (final genre in _genreController.genres) {
                uniqueGenres[genre.name] = genre;
              }
              final List<NovelGenre> uniqueGenreList = uniqueGenres.values.toList();

              // 确保选中的值在列表中存在
              String? currentValue = _selectedGenre.value;
              if (currentValue.isEmpty && uniqueGenreList.isNotEmpty) {
                currentValue = uniqueGenreList.first.name;
                _selectedGenre.value = currentValue;
              } else if (currentValue.isNotEmpty &&
                         !uniqueGenreList.any((NovelGenre g) => g.name == currentValue)) {
                currentValue = uniqueGenreList.isNotEmpty ? uniqueGenreList.first.name : null;
                _selectedGenre.value = currentValue ?? '';
              }

              return DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: '小说类型',
                  border: OutlineInputBorder(),
                ),
                value: currentValue,
                items: uniqueGenreList.map<DropdownMenuItem<String>>((NovelGenre genre) {
                  return DropdownMenuItem<String>(
                    value: genre.name,
                    child: Text(genre.name),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    _selectedGenre.value = value;
                  }
                },
              );
            }),
            const SizedBox(height: 16),
            TextField(
              controller: _initialIdeaController,
              decoration: const InputDecoration(
                labelText: '初始构想（可选）',
                border: OutlineInputBorder(),
                hintText: '请输入你的初始构想或关键元素',
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            Obx(() => SwitchListTile(
                  title: const Text('生成详细背景'),
                  subtitle: const Text('开启后将生成更详细的世界观设定'),
                  value: _isDetailed.value,
                  onChanged: (value) {
                    _isDetailed.value = value;
                  },
                )),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: Obx(() => ElevatedButton(
                onPressed: _isGenerating.value ? null : _generateBackground,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 12.0),
                  child: _isGenerating.value
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 8),
                          Text('生成中...'),
                        ],
                      )
                    : const Text('生成背景'),
                ),
              )),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneratedContent() {
    return Expanded(
      child: Obx(() {
        if (_generatedBackground.value.isEmpty) {
          return const Center(
            child: Text(
              '生成的背景将显示在这里',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          );
        }

        return Card(
          elevation: 2,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '生成的背景',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                SelectableText(
                  _generatedBackground.value,
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.5,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: _generatedBackground.value));
                          Get.snackbar('成功', '背景已复制到剪贴板');
                        },
                        icon: const Icon(Icons.copy),
                        label: const Text('复制'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _showAddToBackgroundDialog(),
                        icon: const Icon(Icons.add),
                        label: const Text('添加到背景'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  Future<void> _generateBackground() async {
    // 验证输入
    if (_titleController.text.isEmpty) {
      Get.snackbar('错误', '请输入小说标题');
      return;
    }

    if (_selectedGenre.value.isEmpty) {
      Get.snackbar('错误', '请选择小说类型');
      return;
    }

    // 开始生成
    _isGenerating.value = true;

    try {
      final background = await _backgroundGeneratorService.generateBackground(
        title: _titleController.text,
        genre: _selectedGenre.value,
        initialIdea: _initialIdeaController.text,
        isDetailed: _isDetailed.value,
      );

      _generatedBackground.value = background;

      // 生成完成后显示确认对话框
      _showGenerationCompleteDialog();
    } catch (e) {
      Get.snackbar('错误', '生成背景失败: $e');
    } finally {
      _isGenerating.value = false;
    }
  }

  /// 显示生成完成对话框
  void _showGenerationCompleteDialog() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('背景生成完成'),
          ],
        ),
        content: const Text('背景已成功生成！您可以选择将其添加到小说背景信息中，或者仅查看内容。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('仅查看'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _addToNovelBackground();
            },
            child: const Text('添加到背景'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  /// 显示添加到背景对话框
  void _showAddToBackgroundDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('添加到背景信息'),
        content: const Text('确定要将生成的背景添加到小说背景信息中吗？这将覆盖当前的背景设置。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _addToNovelBackground();
            },
            child: const Text('确定添加'),
          ),
        ],
      ),
    );
  }

  /// 添加到小说背景
  void _addToNovelBackground() {
    try {
      // 同时更新两个背景变量，确保主页能显示
      _novelController.setNovelBackground(_generatedBackground.value);
      _novelController.updateBackground(_generatedBackground.value);

      Get.snackbar(
        '成功',
        '背景已添加到小说设置中，您可以在主页看到更新的背景信息',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.withOpacity(0.8),
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      Get.snackbar('错误', '添加背景失败: $e');
    }
  }
}
