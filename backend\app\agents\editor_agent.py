"""
校订者智能体 - 逻辑与一致性评论家
"""
from typing import Dict, Any, List
from langchain.prompts import PromptTemplate
from .base_agent import BaseAgent
from .tools import get_common_tools
import json
import os
import logging

logger = logging.getLogger(__name__)

class EditorAgent(BaseAgent):
    """校订者智能体 - 系统的质量保证引擎"""
    
    def __init__(self, llm=None, lorekeeper=None):
        super().__init__(
            name="校订者",
            description="系统的质量保证引擎，负责检测和标记小说中的不一致之处",
            llm=llm,
            tools=get_common_tools(llm) if llm else []
        )
        self.lorekeeper = lorekeeper  # 典籍官引用，用于事实核查
    
    def _get_system_prompt(self) -> str:
        return """
你是校订者智能体，负责小说内容的质量保证。你的职责包括：

1. 逻辑一致性检查：确保情节发展符合逻辑
2. 设定一致性检查：验证内容与世界观设定的一致性
3. 角色一致性检查：确保角色行为符合其性格设定
4. 情节连贯性检查：验证与前文的连接是否自然
5. "爽文"质量检查：确保符合爽文创作要求

检查标准：
- [IsConsistent]: 本章内容是否与已建立的背景设定相矛盾？
- [IsPlausible]: 角色的行为鉴于其性格是否合理？
- [FollowsOutline]: 本章是否遵循了大纲规划？
- [HasShuangPoint]: 是否包含适当的"爽点"？
- [MaintainsPace]: 节奏是否紧凑，符合爽文要求？

你需要使用自我批判机制，通过向典籍官查询相关信息来验证内容的准确性。
对于发现的问题，要提供具体的修改建议。
"""
    
    def _get_prompt_template(self) -> PromptTemplate:
        return PromptTemplate(
            input_variables=["input", "chat_history", "agent_scratchpad"],
            template="""
{system_prompt}

你有以下工具可以使用：
{tools}

审查任务：{input}

聊天历史：{chat_history}

使用以下格式进行推理和行动：

思考：我需要分析章节内容并进行各项一致性检查
行动：[选择一个工具]
行动输入：[工具的输入参数]
观察：[工具的输出结果]
... (可以重复思考/行动/观察)
思考：我现在完成了所有检查，可以给出评估结果
最终答案：[你的审查结果和建议]

开始！

{agent_scratchpad}
""".format(
                system_prompt=self._get_system_prompt(),
                tools="{tools}",
                input="{input}",
                chat_history="{chat_history}",
                agent_scratchpad="{agent_scratchpad}"
            )
        )
    
    def _direct_action(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """直接行动方法"""
        task_type = plan.get("task_type", "unknown")
        
        if task_type == "review_chapter":
            return self._review_chapter(plan)
        elif task_type == "consistency_check":
            return self._consistency_check(plan)
        elif task_type == "quality_assessment":
            return self._quality_assessment(plan)
        else:
            return {"success": False, "error": f"未知任务类型: {task_type}"}
    
    def _review_chapter(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """审查章节"""
        try:
            chapter_number = plan.get("chapter_number", 1)
            chapter_content = plan.get("chapter_content", "")
            chapter_outline = plan.get("chapter_outline", "")
            
            if not chapter_content:
                return {"success": False, "error": "章节内容为空"}
            
            # 执行全面审查
            review_results = {
                "chapter_number": chapter_number,
                "consistency_check": self._check_consistency(chapter_content, chapter_number),
                "character_check": self._check_characters(chapter_content, chapter_number),
                "outline_check": self._check_outline_adherence(chapter_content, chapter_outline),
                "shuang_point_check": self._check_shuang_points(chapter_content, chapter_number),
                "pace_check": self._check_pace(chapter_content),
                "overall_assessment": None
            }
            
            # 生成整体评估
            review_results["overall_assessment"] = self._generate_overall_assessment(review_results)
            
            # 确定是否通过审查
            passed = self._determine_pass_status(review_results)
            
            logger.info(f"第{chapter_number}章审查完成，结果：{'通过' if passed else '需要修改'}")
            
            return {
                "success": True,
                "chapter_number": chapter_number,
                "passed": passed,
                "review_results": review_results,
                "feedback": self._generate_feedback(review_results)
            }
            
        except Exception as e:
            logger.error(f"章节审查失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _check_consistency(self, content: str, chapter_number: int) -> Dict[str, Any]:
        """检查一致性"""
        try:
            # 获取相关设定信息
            if self.lorekeeper:
                context_result = self.lorekeeper.execute({
                    "task_type": "get_context",
                    "context_type": "settings",
                    "chapter_number": chapter_number
                })
                context = context_result.get("context", "") if context_result.get("success") else ""
            else:
                context = self._get_settings_context()
            
            consistency_prompt = f"""
请检查以下章节内容是否与设定保持一致：

设定信息：
{context}

章节内容：
{content}

请分析：
1. 世界观设定是否一致
2. 力量体系是否符合规则
3. "金手指"使用是否合理
4. 地理环境描述是否一致

检查结果（JSON格式）：
{{
    "is_consistent": true/false,
    "issues": ["问题1", "问题2"],
    "suggestions": ["建议1", "建议2"]
}}
"""
            
            response = self.llm.invoke(consistency_prompt)
            
            try:
                result = json.loads(response.content)
            except:
                result = {
                    "is_consistent": True,
                    "issues": [],
                    "suggestions": [],
                    "raw_response": response.content
                }
            
            return result
            
        except Exception as e:
            logger.error(f"一致性检查失败: {str(e)}")
            return {"is_consistent": False, "error": str(e)}
    
    def _check_characters(self, content: str, chapter_number: int) -> Dict[str, Any]:
        """检查角色一致性"""
        try:
            # 获取角色信息
            character_context = self._get_character_context()
            
            character_prompt = f"""
请检查章节中角色的行为是否符合其性格设定：

角色设定：
{character_context}

章节内容：
{content}

请分析：
1. 主角行为是否符合性格
2. 配角表现是否一致
3. 反派角色是否符合设定
4. 对话是否符合角色特点

检查结果（JSON格式）：
{{
    "characters_consistent": true/false,
    "character_issues": {{"角色名": "问题描述"}},
    "suggestions": ["建议1", "建议2"]
}}
"""
            
            response = self.llm.invoke(character_prompt)
            
            try:
                result = json.loads(response.content)
            except:
                result = {
                    "characters_consistent": True,
                    "character_issues": {},
                    "suggestions": [],
                    "raw_response": response.content
                }
            
            return result
            
        except Exception as e:
            logger.error(f"角色检查失败: {str(e)}")
            return {"characters_consistent": False, "error": str(e)}
    
    def _check_outline_adherence(self, content: str, outline: str) -> Dict[str, Any]:
        """检查大纲遵循情况"""
        try:
            outline_prompt = f"""
请检查章节内容是否遵循了预定的大纲：

章节大纲：
{outline}

实际内容：
{content}

请分析：
1. 主要情节点是否覆盖
2. 是否偏离大纲方向
3. 重要事件是否发生
4. 章节目标是否达成

检查结果（JSON格式）：
{{
    "follows_outline": true/false,
    "covered_points": ["已覆盖的情节点"],
    "missing_points": ["遗漏的情节点"],
    "deviations": ["偏离之处"],
    "suggestions": ["建议"]
}}
"""
            
            response = self.llm.invoke(outline_prompt)
            
            try:
                result = json.loads(response.content)
            except:
                result = {
                    "follows_outline": True,
                    "covered_points": [],
                    "missing_points": [],
                    "deviations": [],
                    "suggestions": [],
                    "raw_response": response.content
                }
            
            return result
            
        except Exception as e:
            logger.error(f"大纲检查失败: {str(e)}")
            return {"follows_outline": False, "error": str(e)}
    
    def _check_shuang_points(self, content: str, chapter_number: int) -> Dict[str, Any]:
        """检查"爽点"设置"""
        try:
            shuang_prompt = f"""
请检查第{chapter_number}章是否包含适当的"爽点"：

章节内容：
{content}

请分析：
1. 是否有打脸情节
2. 是否有能力提升
3. 是否有复仇元素
4. 是否有装逼打脸
5. 读者满足感如何

检查结果（JSON格式）：
{{
    "has_shuang_points": true/false,
    "shuang_types": ["打脸", "升级", "复仇"],
    "satisfaction_level": "高/中/低",
    "suggestions": ["建议"]
}}
"""
            
            response = self.llm.invoke(shuang_prompt)
            
            try:
                result = json.loads(response.content)
            except:
                result = {
                    "has_shuang_points": False,
                    "shuang_types": [],
                    "satisfaction_level": "中",
                    "suggestions": [],
                    "raw_response": response.content
                }
            
            return result
            
        except Exception as e:
            logger.error(f"爽点检查失败: {str(e)}")
            return {"has_shuang_points": False, "error": str(e)}
    
    def _check_pace(self, content: str) -> Dict[str, Any]:
        """检查节奏"""
        try:
            pace_prompt = f"""
请检查章节的节奏是否符合"爽文"要求：

章节内容：
{content}

请分析：
1. 节奏是否紧凑
2. 是否有拖沓之处
3. 对话是否推进情节
4. 描写是否适度
5. 整体阅读体验

检查结果（JSON格式）：
{{
    "pace_appropriate": true/false,
    "pace_issues": ["问题1", "问题2"],
    "strengths": ["优点1", "优点2"],
    "suggestions": ["建议1", "建议2"]
}}
"""
            
            response = self.llm.invoke(pace_prompt)
            
            try:
                result = json.loads(response.content)
            except:
                result = {
                    "pace_appropriate": True,
                    "pace_issues": [],
                    "strengths": [],
                    "suggestions": [],
                    "raw_response": response.content
                }
            
            return result
            
        except Exception as e:
            logger.error(f"节奏检查失败: {str(e)}")
            return {"pace_appropriate": False, "error": str(e)}
    
    def _generate_overall_assessment(self, review_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成整体评估"""
        try:
            # 计算各项得分
            scores = {
                "consistency": 1 if review_results["consistency_check"].get("is_consistent", False) else 0,
                "characters": 1 if review_results["character_check"].get("characters_consistent", False) else 0,
                "outline": 1 if review_results["outline_check"].get("follows_outline", False) else 0,
                "shuang_points": 1 if review_results["shuang_point_check"].get("has_shuang_points", False) else 0,
                "pace": 1 if review_results["pace_check"].get("pace_appropriate", False) else 0
            }
            
            total_score = sum(scores.values())
            max_score = len(scores)
            
            # 确定质量等级
            if total_score >= 4:
                quality = "优秀"
            elif total_score >= 3:
                quality = "良好"
            elif total_score >= 2:
                quality = "一般"
            else:
                quality = "需要改进"
            
            return {
                "scores": scores,
                "total_score": total_score,
                "max_score": max_score,
                "quality": quality,
                "pass_threshold": 3
            }
            
        except Exception as e:
            logger.error(f"整体评估失败: {str(e)}")
            return {"quality": "未知", "error": str(e)}
    
    def _determine_pass_status(self, review_results: Dict[str, Any]) -> bool:
        """确定是否通过审查"""
        try:
            overall = review_results.get("overall_assessment", {})
            total_score = overall.get("total_score", 0)
            pass_threshold = overall.get("pass_threshold", 3)
            
            return total_score >= pass_threshold
            
        except Exception as e:
            logger.error(f"通过状态判断失败: {str(e)}")
            return False
    
    def _generate_feedback(self, review_results: Dict[str, Any]) -> str:
        """生成反馈意见"""
        try:
            feedback_parts = []
            
            # 整体评估
            overall = review_results.get("overall_assessment", {})
            quality = overall.get("quality", "未知")
            feedback_parts.append(f"整体质量：{quality}")
            
            # 具体问题
            issues = []
            
            # 一致性问题
            consistency = review_results.get("consistency_check", {})
            if not consistency.get("is_consistent", True):
                issues.extend(consistency.get("issues", []))
            
            # 角色问题
            characters = review_results.get("character_check", {})
            if not characters.get("characters_consistent", True):
                for char, issue in characters.get("character_issues", {}).items():
                    issues.append(f"{char}: {issue}")
            
            # 大纲问题
            outline = review_results.get("outline_check", {})
            if not outline.get("follows_outline", True):
                issues.extend(outline.get("missing_points", []))
                issues.extend(outline.get("deviations", []))
            
            # 爽点问题
            shuang = review_results.get("shuang_point_check", {})
            if not shuang.get("has_shuang_points", False):
                issues.append("缺乏足够的'爽点'，建议增加打脸或升级情节")
            
            # 节奏问题
            pace = review_results.get("pace_check", {})
            if not pace.get("pace_appropriate", True):
                issues.extend(pace.get("pace_issues", []))
            
            if issues:
                feedback_parts.append("发现的问题：")
                for i, issue in enumerate(issues, 1):
                    feedback_parts.append(f"{i}. {issue}")
            
            # 建议
            all_suggestions = []
            for check_result in review_results.values():
                if isinstance(check_result, dict) and "suggestions" in check_result:
                    all_suggestions.extend(check_result["suggestions"])
            
            if all_suggestions:
                feedback_parts.append("修改建议：")
                for i, suggestion in enumerate(set(all_suggestions), 1):
                    feedback_parts.append(f"{i}. {suggestion}")
            
            return "\n".join(feedback_parts)
            
        except Exception as e:
            logger.error(f"反馈生成失败: {str(e)}")
            return f"审查完成，但反馈生成失败：{str(e)}"
    
    def _get_settings_context(self) -> str:
        """获取设定上下文"""
        try:
            context_parts = []
            
            # 读取世界观设定
            if os.path.exists("data/world_bible.json"):
                with open("data/world_bible.json", 'r', encoding='utf-8') as f:
                    world_bible = json.load(f)
                context_parts.append(json.dumps(world_bible, ensure_ascii=False, indent=2))
            
            return "\n\n".join(context_parts)
            
        except Exception as e:
            logger.warning(f"获取设定上下文失败: {str(e)}")
            return ""
    
    def _get_character_context(self) -> str:
        """获取角色上下文"""
        try:
            # 读取角色设定
            if os.path.exists("data/characters.json"):
                with open("data/characters.json", 'r', encoding='utf-8') as f:
                    characters = json.load(f)
                return json.dumps(characters, ensure_ascii=False, indent=2)
            
            return ""
            
        except Exception as e:
            logger.warning(f"获取角色上下文失败: {str(e)}")
            return ""
