#!/bin/bash

# =============================================================================
# AI智能体后端一键部署脚本 - 宝塔面板版
# 作者: AI Assistant
# 版本: 1.0.0
# 描述: 在宝塔面板服务器上一键部署AI智能体后端服务
# =============================================================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="ai_agent_backend"
PROJECT_PATH="/www/wwwroot/${PROJECT_NAME}"
PYTHON_VERSION="3.9"
PORT="8000"
DOMAIN=""  # 可选，如果有域名的话

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

print_success() { print_message "$GREEN" "✅ $1"; }
print_info() { print_message "$BLUE" "ℹ️  $1"; }
print_warning() { print_message "$YELLOW" "⚠️  $1"; }
print_error() { print_message "$RED" "❌ $1"; }
print_step() { print_message "$PURPLE" "🚀 $1"; }

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "请使用root用户运行此脚本"
        exit 1
    fi
}

# 检查宝塔面板是否安装
check_bt_panel() {
    if ! command -v bt &> /dev/null; then
        print_error "未检测到宝塔面板，请先安装宝塔面板"
        print_info "安装命令: wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && bash install.sh"
        exit 1
    fi
    print_success "检测到宝塔面板"
}

# 安装Python环境
install_python() {
    print_step "安装Python环境..."
    
    # 检查Python是否已安装
    if command -v python3 &> /dev/null; then
        local python_ver=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
        print_info "检测到Python版本: $python_ver"
        if [[ $(echo "$python_ver >= 3.8" | bc -l) -eq 1 ]]; then
            print_success "Python版本满足要求"
            return
        fi
    fi
    
    # 安装Python
    apt update
    apt install -y python3 python3-pip python3-venv python3-dev
    print_success "Python安装完成"
}

# 创建项目目录
create_project_dir() {
    print_step "创建项目目录..."
    
    if [ -d "$PROJECT_PATH" ]; then
        print_warning "项目目录已存在，正在备份..."
        mv "$PROJECT_PATH" "${PROJECT_PATH}_backup_$(date +%Y%m%d_%H%M%S)"
    fi
    
    mkdir -p "$PROJECT_PATH"
    cd "$PROJECT_PATH"
    print_success "项目目录创建完成: $PROJECT_PATH"
}

# 创建虚拟环境
create_venv() {
    print_step "创建Python虚拟环境..."
    
    python3 -m venv venv
    source venv/bin/activate
    pip install --upgrade pip
    print_success "虚拟环境创建完成"
}

# 创建项目结构
create_project_structure() {
    print_step "创建项目结构..."
    
    # 创建目录结构
    mkdir -p app/{routers,agents,config,models,database}
    mkdir -p logs uploads downloads static templates
    
    print_success "项目结构创建完成"
}

# 创建requirements.txt
create_requirements() {
    print_step "创建依赖文件..."
    
    cat > requirements.txt << 'EOF'
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dotenv==1.0.0
httpx==0.25.2
langchain==0.1.0
langchain-openai==0.0.2
langchain-community==0.0.10
langgraph==0.0.20
faiss-cpu==1.7.4
numpy==1.24.3
pandas==2.1.4
aiofiles==23.2.1
jinja2==3.1.2
sqlalchemy==2.0.23
alembic==1.13.1
redis==5.0.1
celery==5.3.4
gunicorn==21.2.0
EOF
    
    print_success "依赖文件创建完成"
}

# 安装依赖
install_dependencies() {
    print_step "安装Python依赖包..."
    
    source venv/bin/activate
    pip install -r requirements.txt
    print_success "依赖包安装完成"
}

# 创建配置文件
create_config() {
    print_step "创建配置文件..."
    
    # 生成随机密钥
    SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
    
    cat > .env << EOF
# 应用配置
APP_NAME="AI Agent Novel Backend"
APP_VERSION="1.0.0"
DEBUG=False
SECRET_KEY="$SECRET_KEY"

# 服务器配置
HOST=0.0.0.0
PORT=$PORT

# 数据库配置
DATABASE_URL="sqlite:///./ai_agent.db"

# Redis配置
REDIS_URL="redis://localhost:6379/0"

# 默认LLM配置
DEFAULT_LLM_PROVIDER="openai"
DEFAULT_LLM_MODEL="gpt-4-turbo-preview"
DEFAULT_LLM_TEMPERATURE=0.7
DEFAULT_LLM_MAX_TOKENS=4000

# 文件存储
UPLOAD_DIR="$PROJECT_PATH/uploads"
DOWNLOAD_DIR="$PROJECT_PATH/downloads"

# 日志配置
LOG_LEVEL="INFO"
LOG_FILE="$PROJECT_PATH/logs/app.log"

# CORS配置
ALLOWED_ORIGINS="*"
EOF
    
    print_success "配置文件创建完成"
}

# 创建应用代码
create_app_code() {
    print_step "创建应用代码..."

    # 创建主应用文件
    cat > app/__init__.py << 'EOF'
"""AI Agent Backend Application"""
__version__ = "1.0.0"
EOF

    # 创建主入口文件
    cat > app/main.py << 'EOF'
"""
AI Agent Backend Main Application
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 创建FastAPI应用
app = FastAPI(
    title=os.getenv("APP_NAME", "AI Agent Backend"),
    version=os.getenv("APP_VERSION", "1.0.0"),
    description="AI智能体小说生成后端服务",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=os.getenv("ALLOWED_ORIGINS", "*").split(","),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 导入路由
from .routers import ai_agents

# 注册路由
app.include_router(ai_agents.router, prefix="/api/v1/ai-agents", tags=["AI Agents"])

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "AI Agent Backend is running!",
        "version": os.getenv("APP_VERSION", "1.0.0"),
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "ai-agent-backend",
        "version": os.getenv("APP_VERSION", "1.0.0")
    }

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    return JSONResponse(
        status_code=500,
        content={"detail": f"Internal server error: {str(exc)}"}
    )
EOF

    # 创建路由文件
    mkdir -p app/routers
    cat > app/routers/__init__.py << 'EOF'
"""Routers package"""
EOF

    cat > app/routers/ai_agents.py << 'EOF'
"""
AI Agents API Routes
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Optional, Dict, Any
import uuid
import asyncio
import json
from datetime import datetime

router = APIRouter()

# 模拟任务存储（生产环境应使用数据库）
tasks = {}

class NovelGenerationRequest(BaseModel):
    """小说生成请求"""
    prompt: str
    target_chapters: int = 10
    genre: str = "修仙"
    provider: str = "openai"
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    model: str = "gpt-4-turbo-preview"
    temperature: float = 0.7
    max_tokens: int = 4000

@router.post("/generate")
async def start_generation(
    request: NovelGenerationRequest,
    background_tasks: BackgroundTasks
):
    """开始生成小说"""
    task_id = str(uuid.uuid4())

    # 创建任务记录
    tasks[task_id] = {
        "id": task_id,
        "status": "pending",
        "progress": {
            "current_chapter": 0,
            "target_chapters": request.target_chapters,
            "current_phase": "initialization"
        },
        "request": request.dict(),
        "created_at": datetime.now().isoformat(),
        "result": None
    }

    # 启动后台任务
    background_tasks.add_task(simulate_generation, task_id, request)

    return {
        "success": True,
        "task_id": task_id,
        "message": "小说生成任务已启动"
    }

@router.get("/status/{task_id}")
async def get_generation_status(task_id: str):
    """获取生成状态"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    task = tasks[task_id]
    return {
        "success": True,
        "task_id": task_id,
        "status": task["status"],
        "progress": task["progress"],
        "created_at": task["created_at"]
    }

@router.get("/result/{task_id}")
async def get_generation_result(task_id: str):
    """获取生成结果"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    task = tasks[task_id]
    if task["status"] != "completed":
        raise HTTPException(status_code=400, detail="任务尚未完成")

    return task["result"]

@router.delete("/cancel/{task_id}")
async def cancel_generation(task_id: str):
    """取消生成任务"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    tasks[task_id]["status"] = "cancelled"
    return {
        "success": True,
        "message": "任务已取消"
    }

async def simulate_generation(task_id: str, request: NovelGenerationRequest):
    """模拟小说生成过程"""
    try:
        task = tasks[task_id]
        task["status"] = "running"

        # 模拟各个阶段
        phases = [
            ("initialization", "正在初始化..."),
            ("world_building", "构建世界观..."),
            ("generation", "生成章节内容..."),
            ("review", "审查和优化..."),
            ("completion", "完成生成...")
        ]

        for i, (phase, description) in enumerate(phases):
            task["progress"]["current_phase"] = phase
            await asyncio.sleep(2)  # 模拟处理时间

            if phase == "generation":
                # 模拟章节生成
                for chapter in range(1, request.target_chapters + 1):
                    if task["status"] == "cancelled":
                        return

                    task["progress"]["current_chapter"] = chapter
                    await asyncio.sleep(1)  # 模拟每章生成时间

        # 生成完成
        task["status"] = "completed"
        task["result"] = {
            "success": True,
            "title": f"基于'{request.prompt}'的{request.genre}小说",
            "completed_chapters": request.target_chapters,
            "target_chapters": request.target_chapters,
            "total_words": request.target_chapters * 2000,  # 模拟字数
            "generated_at": datetime.now().isoformat(),
            "world_bible": "世界观设定内容...",
            "characters": "角色档案内容...",
            "outline": "情节大纲内容...",
            "chapters": [
                {
                    "chapter": i,
                    "title": f"第{i}章",
                    "content": f"第{i}章的内容...",
                    "word_count": 2000
                }
                for i in range(1, request.target_chapters + 1)
            ]
        }

    except Exception as e:
        task["status"] = "failed"
        task["error"] = str(e)

@router.get("/tasks")
async def get_user_tasks():
    """获取用户任务列表"""
    return {
        "success": True,
        "tasks": list(tasks.values())
    }

@router.get("/download/{task_id}")
async def download_novel(task_id: str):
    """下载小说"""
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    task = tasks[task_id]
    if task["status"] != "completed":
        raise HTTPException(status_code=400, detail="任务尚未完成")

    return {
        "success": True,
        "download_url": f"/api/v1/ai-agents/files/{task_id}.txt",
        "title": task["result"]["title"],
        "word_count": task["result"]["total_words"],
        "chapters": task["result"]["completed_chapters"]
    }
EOF

    print_success "应用代码创建完成"
}

# 创建启动脚本
create_startup_script() {
    print_step "创建启动脚本..."

    cat > run.py << 'EOF'
#!/usr/bin/env python3
"""
AI Agent Backend 启动脚本
"""
import os
import sys
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """启动应用"""
    # 创建必要的目录
    os.makedirs("logs", exist_ok=True)
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("downloads", exist_ok=True)

    # 启动服务
    uvicorn.run(
        "app.main:app",
        host=os.getenv("HOST", "0.0.0.0"),
        port=int(os.getenv("PORT", 8000)),
        reload=os.getenv("DEBUG", "False").lower() == "true",
        workers=1 if os.getenv("DEBUG", "False").lower() == "true" else 4,
        log_level=os.getenv("LOG_LEVEL", "info").lower(),
        access_log=True,
    )

if __name__ == "__main__":
    main()
EOF

    chmod +x run.py
    print_success "启动脚本创建完成"
}

# 配置系统服务
setup_systemd_service() {
    print_step "配置系统服务..."

    cat > /etc/systemd/system/${PROJECT_NAME}.service << EOF
[Unit]
Description=AI Agent Backend Service
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=$PROJECT_PATH
Environment=PATH=$PROJECT_PATH/venv/bin
ExecStart=$PROJECT_PATH/venv/bin/python $PROJECT_PATH/run.py
Restart=always
RestartSec=3
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable ${PROJECT_NAME}
    print_success "系统服务配置完成"
}

# 配置Nginx反向代理
setup_nginx() {
    print_step "配置Nginx反向代理..."

    # 检查是否安装了Nginx
    if ! command -v nginx &> /dev/null; then
        print_info "安装Nginx..."
        apt install -y nginx
    fi

    # 创建Nginx配置
    cat > /etc/nginx/sites-available/${PROJECT_NAME} << EOF
server {
    listen 80;
    server_name _;

    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:$PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 文档页面
    location /docs {
        proxy_pass http://127.0.0.1:$PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:$PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # 静态文件
    location /static/ {
        alias $PROJECT_PATH/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }

    # 默认页面
    location / {
        proxy_pass http://127.0.0.1:$PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

    # 启用站点
    ln -sf /etc/nginx/sites-available/${PROJECT_NAME} /etc/nginx/sites-enabled/

    # 测试配置
    nginx -t
    systemctl reload nginx

    print_success "Nginx配置完成"
}

# 配置防火墙
setup_firewall() {
    print_step "配置防火墙..."

    # 检查是否安装了ufw
    if command -v ufw &> /dev/null; then
        ufw allow 80/tcp
        ufw allow 443/tcp
        ufw allow $PORT/tcp
        print_success "防火墙配置完成"
    else
        print_warning "未检测到ufw防火墙，请手动开放端口80、443、$PORT"
    fi
}

# 设置权限
set_permissions() {
    print_step "设置文件权限..."

    chown -R www-data:www-data $PROJECT_PATH
    chmod -R 755 $PROJECT_PATH
    chmod +x $PROJECT_PATH/run.py

    print_success "文件权限设置完成"
}

# 启动服务
start_services() {
    print_step "启动服务..."

    systemctl start ${PROJECT_NAME}
    systemctl start nginx

    # 等待服务启动
    sleep 5

    # 检查服务状态
    if systemctl is-active --quiet ${PROJECT_NAME}; then
        print_success "AI Agent Backend服务启动成功"
    else
        print_error "AI Agent Backend服务启动失败"
        systemctl status ${PROJECT_NAME}
        exit 1
    fi

    if systemctl is-active --quiet nginx; then
        print_success "Nginx服务运行正常"
    else
        print_warning "Nginx服务可能有问题"
    fi
}

# 测试部署
test_deployment() {
    print_step "测试部署..."

    # 等待服务完全启动
    sleep 10

    # 测试健康检查
    if curl -f -s http://localhost/health > /dev/null; then
        print_success "健康检查通过"
    else
        print_error "健康检查失败"
        return 1
    fi

    # 测试API文档
    if curl -f -s http://localhost/docs > /dev/null; then
        print_success "API文档访问正常"
    else
        print_warning "API文档可能无法访问"
    fi

    print_success "部署测试完成"
}

# 显示部署信息
show_deployment_info() {
    local server_ip=$(curl -s ifconfig.me || echo "YOUR_SERVER_IP")

    echo
    print_step "🎉 部署完成！"
    echo
    print_info "服务信息:"
    echo "  📍 项目路径: $PROJECT_PATH"
    echo "  🌐 服务端口: $PORT"
    echo "  📊 服务状态: $(systemctl is-active ${PROJECT_NAME})"
    echo
    print_info "访问地址:"
    echo "  🏠 主页: http://$server_ip/"
    echo "  📚 API文档: http://$server_ip/docs"
    echo "  ❤️  健康检查: http://$server_ip/health"
    echo "  🔗 API基础URL: http://$server_ip/api/v1/ai-agents"
    echo
    print_info "管理命令:"
    echo "  🔄 重启服务: systemctl restart ${PROJECT_NAME}"
    echo "  📋 查看状态: systemctl status ${PROJECT_NAME}"
    echo "  📝 查看日志: journalctl -u ${PROJECT_NAME} -f"
    echo "  🛑 停止服务: systemctl stop ${PROJECT_NAME}"
    echo
    print_info "配置文件:"
    echo "  ⚙️  环境配置: $PROJECT_PATH/.env"
    echo "  🌐 Nginx配置: /etc/nginx/sites-available/${PROJECT_NAME}"
    echo "  🔧 系统服务: /etc/systemd/system/${PROJECT_NAME}.service"
    echo
    print_warning "下一步:"
    echo "  1. 在Flutter应用中更新API地址为: http://$server_ip/api/v1/ai-agents"
    echo "  2. 在应用设置中配置您的AI模型API密钥"
    echo "  3. 测试小说生成功能"
    echo
}

# 主函数
main() {
    echo
    print_step "🚀 开始部署AI智能体后端服务"
    echo

    # 执行部署步骤
    check_root
    check_bt_panel
    install_python
    create_project_dir
    create_venv
    create_project_structure
    create_requirements
    install_dependencies
    create_config
    create_app_code
    create_startup_script
    setup_systemd_service
    setup_nginx
    setup_firewall
    set_permissions
    start_services
    test_deployment
    show_deployment_info

    print_success "🎊 AI智能体后端部署完成！"
}

# 错误处理
trap 'print_error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 运行主函数
main "$@"
