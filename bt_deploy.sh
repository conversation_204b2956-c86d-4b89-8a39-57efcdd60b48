#!/bin/bash

# =============================================================================
# AI智能体后端一键部署脚本 - 宝塔面板专用版
# 使用方法: bash bt_deploy.sh
# =============================================================================

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

# 配置
PROJECT_NAME="ai_agent_backend"
PROJECT_PATH="/www/wwwroot/${PROJECT_NAME}"
PORT="8000"

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查宝塔面板
check_bt() {
    if ! command -v bt &> /dev/null; then
        print_error "请先安装宝塔面板！"
        print_info "安装命令: wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && bash install.sh"
        exit 1
    fi
    print_success "检测到宝塔面板"
}

# 创建项目
create_project() {
    print_info "创建项目目录..."

    # 备份现有项目
    if [ -d "$PROJECT_PATH" ]; then
        mv "$PROJECT_PATH" "${PROJECT_PATH}_backup_$(date +%Y%m%d_%H%M%S)"
    fi

    mkdir -p "$PROJECT_PATH"
    cd "$PROJECT_PATH"

    # 创建目录结构
    mkdir -p app/{routers,agents,config,models}
    mkdir -p logs uploads downloads static

    print_success "项目目录创建完成"
}

# 创建Python环境
setup_python() {
    print_info "设置Python环境..."

    cd "$PROJECT_PATH"
    python3 -m venv venv
    source venv/bin/activate
    pip install --upgrade pip

    print_success "Python环境设置完成"
}

# 创建应用文件
create_app() {
    print_info "创建应用文件..."

    # requirements.txt
    cat > requirements.txt << 'EOF'
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-dotenv==1.0.0
python-multipart==0.0.6
aiofiles==23.2.1
EOF

    # .env配置
    SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
    cat > .env << EOF
APP_NAME="AI Agent Backend"
DEBUG=False
SECRET_KEY="$SECRET_KEY"
HOST=0.0.0.0
PORT=$PORT
ALLOWED_ORIGINS="*"
EOF

    # 主应用
    cat > app/__init__.py << 'EOF'
"""AI Agent Backend"""
EOF

    print_success "基础文件创建完成"
}

# 创建主应用代码
create_main_app() {
    print_info "创建主应用代码..."

    cat > app/main.py << 'EOF'
from fastapi import FastAPI, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Dict
import uuid
import asyncio
import os
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

app = FastAPI(
    title="AI Agent Backend",
    description="AI智能体小说生成后端",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 任务存储
tasks = {}

class NovelRequest(BaseModel):
    prompt: str
    target_chapters: int = 10
    genre: str = "修仙"
    provider: str = "openai"
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    model: str = "gpt-4-turbo-preview"
    temperature: float = 0.7
    max_tokens: int = 4000

@app.get("/")
async def root():
    return {"message": "AI Agent Backend is running!", "docs": "/docs"}

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "ai-agent-backend"}

@app.post("/api/v1/ai-agents/generate")
async def start_generation(request: NovelRequest, background_tasks: BackgroundTasks):
    task_id = str(uuid.uuid4())

    tasks[task_id] = {
        "id": task_id,
        "status": "pending",
        "progress": {"current_chapter": 0, "target_chapters": request.target_chapters},
        "created_at": datetime.now().isoformat()
    }

    background_tasks.add_task(simulate_generation, task_id, request)

    return {"success": True, "task_id": task_id, "message": "任务已启动"}

@app.get("/api/v1/ai-agents/status/{task_id}")
async def get_status(task_id: str):
    if task_id not in tasks:
        raise HTTPException(404, "任务不存在")
    return {"success": True, **tasks[task_id]}

@app.get("/api/v1/ai-agents/result/{task_id}")
async def get_result(task_id: str):
    if task_id not in tasks:
        raise HTTPException(404, "任务不存在")
    task = tasks[task_id]
    if task["status"] != "completed":
        raise HTTPException(400, "任务未完成")
    return task.get("result", {})

async def simulate_generation(task_id: str, request: NovelRequest):
    """模拟生成过程"""
    try:
        task = tasks[task_id]
        task["status"] = "running"

        # 模拟生成过程
        for i in range(1, request.target_chapters + 1):
            task["progress"]["current_chapter"] = i
            await asyncio.sleep(2)  # 模拟处理时间

        # 完成
        task["status"] = "completed"
        task["result"] = {
            "success": True,
            "title": f"基于'{request.prompt}'的{request.genre}小说",
            "chapters": request.target_chapters,
            "words": request.target_chapters * 2000,
            "content": "这里是生成的小说内容..."
        }
    except Exception as e:
        task["status"] = "failed"
        task["error"] = str(e)
EOF

    # 启动脚本
    cat > run.py << 'EOF'
#!/usr/bin/env python3
import uvicorn
import os

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8000)),
        reload=False
    )
EOF
    chmod +x run.py

    print_success "主应用代码创建完成"
}

# 安装依赖
install_deps() {
    print_info "安装依赖..."

    cd "$PROJECT_PATH"
    source venv/bin/activate
    pip install -r requirements.txt

    print_success "依赖安装完成"
}

# 设置权限
set_permissions() {
    print_info "设置权限..."

    chown -R www:www "$PROJECT_PATH"
    chmod -R 755 "$PROJECT_PATH"

    print_success "权限设置完成"
}

# 创建启动服务
create_service() {
    print_info "创建系统服务..."

    cat > /etc/systemd/system/ai-agent.service << EOF
[Unit]
Description=AI Agent Backend
After=network.target

[Service]
Type=simple
User=www
WorkingDirectory=$PROJECT_PATH
Environment=PATH=$PROJECT_PATH/venv/bin
ExecStart=$PROJECT_PATH/venv/bin/python $PROJECT_PATH/run.py
Restart=always

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable ai-agent
    systemctl start ai-agent

    print_success "服务创建完成"
}

# 测试部署
test_deployment() {
    print_info "测试部署..."

    sleep 5

    if curl -f -s "http://localhost:$PORT/health" > /dev/null; then
        print_success "服务运行正常"
    else
        print_warning "服务可能未正常启动，请检查日志"
        systemctl status ai-agent
    fi
}

# 显示结果
show_result() {
    local ip=$(curl -s ifconfig.me 2>/dev/null || echo "YOUR_SERVER_IP")

    echo
    print_success "🎉 部署完成！"
    echo
    echo "📍 项目路径: $PROJECT_PATH"
    echo "🌐 访问地址: http://$ip:$PORT"
    echo "📚 API文档: http://$ip:$PORT/docs"
    echo "❤️  健康检查: http://$ip:$PORT/health"
    echo "🔗 API地址: http://$ip:$PORT/api/v1/ai-agents"
    echo
    echo "🔧 管理命令:"
    echo "  systemctl status ai-agent    # 查看状态"
    echo "  systemctl restart ai-agent   # 重启服务"
    echo "  systemctl stop ai-agent      # 停止服务"
    echo
    echo "📱 Flutter应用配置:"
    echo "  将API地址设置为: http://$ip:$PORT/api/v1/ai-agents"
    echo
}

# 主函数
main() {
    echo "🚀 开始部署AI智能体后端..."

    check_bt
    create_project
    setup_python
    create_app
    create_main_app
    install_deps
    set_permissions
    create_service
    test_deployment
    show_result

    print_success "✅ 部署完成！"
}

# 运行
main "$@"

# 检查宝塔面板
check_bt() {
    if ! command -v bt &> /dev/null; then
        print_error "请先安装宝塔面板！"
        print_info "安装命令: wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && bash install.sh"
        exit 1
    fi
    print_success "检测到宝塔面板"
}

# 创建项目
create_project() {
    print_info "创建项目目录..."
    
    # 备份现有项目
    if [ -d "$PROJECT_PATH" ]; then
        mv "$PROJECT_PATH" "${PROJECT_PATH}_backup_$(date +%Y%m%d_%H%M%S)"
    fi
    
    mkdir -p "$PROJECT_PATH"
    cd "$PROJECT_PATH"
    
    # 创建目录结构
    mkdir -p app/{routers,agents,config,models}
    mkdir -p logs uploads downloads static
    
    print_success "项目目录创建完成"
}

# 创建Python环境
setup_python() {
    print_info "设置Python环境..."
    
    cd "$PROJECT_PATH"
    python3 -m venv venv
    source venv/bin/activate
    pip install --upgrade pip
    
    print_success "Python环境设置完成"
}

# 创建应用文件
create_app() {
    print_info "创建应用文件..."
    
    # requirements.txt
    cat > requirements.txt << 'EOF'
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-dotenv==1.0.0
python-multipart==0.0.6
aiofiles==23.2.1
EOF

    # .env配置
    SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_urlsafe(32))")
    cat > .env << EOF
APP_NAME="AI Agent Backend"
DEBUG=False
SECRET_KEY="$SECRET_KEY"
HOST=0.0.0.0
PORT=$PORT
ALLOWED_ORIGINS="*"
EOF

    # 主应用
    cat > app/__init__.py << 'EOF'
"""AI Agent Backend"""
EOF

    cat > app/main.py << 'EOF'
from fastapi import FastAPI, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Dict
import uuid
import asyncio
import os
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

app = FastAPI(
    title="AI Agent Backend",
    description="AI智能体小说生成后端",
    version="1.0.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 任务存储
tasks = {}

class NovelRequest(BaseModel):
    prompt: str
    target_chapters: int = 10
    genre: str = "修仙"
    provider: str = "openai"
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    model: str = "gpt-4-turbo-preview"
    temperature: float = 0.7
    max_tokens: int = 4000

@app.get("/")
async def root():
    return {"message": "AI Agent Backend is running!", "docs": "/docs"}

@app.get("/health")
async def health():
    return {"status": "healthy", "service": "ai-agent-backend"}

@app.post("/api/v1/ai-agents/generate")
async def start_generation(request: NovelRequest, background_tasks: BackgroundTasks):
    task_id = str(uuid.uuid4())
    
    tasks[task_id] = {
        "id": task_id,
        "status": "pending",
        "progress": {"current_chapter": 0, "target_chapters": request.target_chapters},
        "created_at": datetime.now().isoformat()
    }
    
    background_tasks.add_task(simulate_generation, task_id, request)
    
    return {"success": True, "task_id": task_id, "message": "任务已启动"}

@app.get("/api/v1/ai-agents/status/{task_id}")
async def get_status(task_id: str):
    if task_id not in tasks:
        raise HTTPException(404, "任务不存在")
    return {"success": True, **tasks[task_id]}

@app.get("/api/v1/ai-agents/result/{task_id}")
async def get_result(task_id: str):
    if task_id not in tasks:
        raise HTTPException(404, "任务不存在")
    task = tasks[task_id]
    if task["status"] != "completed":
        raise HTTPException(400, "任务未完成")
    return task.get("result", {})

async def simulate_generation(task_id: str, request: NovelRequest):
    """模拟生成过程"""
    try:
        task = tasks[task_id]
        task["status"] = "running"
        
        # 模拟生成过程
        for i in range(1, request.target_chapters + 1):
            task["progress"]["current_chapter"] = i
            await asyncio.sleep(2)  # 模拟处理时间
        
        # 完成
        task["status"] = "completed"
        task["result"] = {
            "success": True,
            "title": f"基于'{request.prompt}'的{request.genre}小说",
            "chapters": request.target_chapters,
            "words": request.target_chapters * 2000,
            "content": "这里是生成的小说内容..."
        }
    except Exception as e:
        task["status"] = "failed"
        task["error"] = str(e)
EOF

    # 启动脚本
    cat > run.py << 'EOF'
#!/usr/bin/env python3
import uvicorn
import os

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8000)),
        reload=False
    )
EOF
    chmod +x run.py
    
    print_success "应用文件创建完成"
}

# 安装依赖
install_deps() {
    print_info "安装依赖..."
    
    cd "$PROJECT_PATH"
    source venv/bin/activate
    pip install -r requirements.txt
    
    print_success "依赖安装完成"
}

# 设置权限
set_permissions() {
    print_info "设置权限..."
    
    chown -R www:www "$PROJECT_PATH"
    chmod -R 755 "$PROJECT_PATH"
    
    print_success "权限设置完成"
}

# 创建启动服务
create_service() {
    print_info "创建系统服务..."
    
    cat > /etc/systemd/system/ai-agent.service << EOF
[Unit]
Description=AI Agent Backend
After=network.target

[Service]
Type=simple
User=www
WorkingDirectory=$PROJECT_PATH
Environment=PATH=$PROJECT_PATH/venv/bin
ExecStart=$PROJECT_PATH/venv/bin/python $PROJECT_PATH/run.py
Restart=always

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable ai-agent
    systemctl start ai-agent
    
    print_success "服务创建完成"
}

# 配置宝塔反向代理
setup_bt_proxy() {
    print_info "请在宝塔面板中手动配置反向代理："
    echo "1. 进入宝塔面板 → 网站 → 添加站点"
    echo "2. 在站点设置 → 反向代理中添加："
    echo "   代理名称: ai-agent"
    echo "   目标URL: http://127.0.0.1:$PORT"
    echo "   发送域名: \$host"
    echo "3. 或者直接通过IP访问: http://YOUR_IP:$PORT"
}

# 测试部署
test_deployment() {
    print_info "测试部署..."
    
    sleep 5
    
    if curl -f -s "http://localhost:$PORT/health" > /dev/null; then
        print_success "服务运行正常"
    else
        print_warning "服务可能未正常启动，请检查日志"
        systemctl status ai-agent
    fi
}

# 显示结果
show_result() {
    local ip=$(curl -s ifconfig.me 2>/dev/null || echo "YOUR_SERVER_IP")
    
    echo
    print_success "🎉 部署完成！"
    echo
    echo "📍 项目路径: $PROJECT_PATH"
    echo "🌐 访问地址: http://$ip:$PORT"
    echo "📚 API文档: http://$ip:$PORT/docs"
    echo "❤️  健康检查: http://$ip:$PORT/health"
    echo "🔗 API地址: http://$ip:$PORT/api/v1/ai-agents"
    echo
    echo "🔧 管理命令:"
    echo "  systemctl status ai-agent    # 查看状态"
    echo "  systemctl restart ai-agent   # 重启服务"
    echo "  systemctl stop ai-agent      # 停止服务"
    echo
    echo "📱 Flutter应用配置:"
    echo "  将API地址设置为: http://$ip:$PORT/api/v1/ai-agents"
    echo
}

# 主函数
main() {
    echo "🚀 开始部署AI智能体后端..."
    
    check_bt
    create_project
    setup_python
    create_app
    install_deps
    set_permissions
    create_service
    test_deployment
    setup_bt_proxy
    show_result
    
    print_success "✅ 部署完成！"
}

# 运行
main "$@"
