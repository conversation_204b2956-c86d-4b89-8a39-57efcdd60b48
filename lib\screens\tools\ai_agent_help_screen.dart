import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AIAgentHelpScreen extends StatelessWidget {
  const AIAgentHelpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI智能体系统说明'),
        backgroundColor: Colors.purple.shade100,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 系统概述
            _buildSection(
              '系统概述',
              Icons.info_outline,
              Colors.blue,
              [
                '岱宗AI自主创作智能体系统是一个基于多智能体架构的小说创作系统，'
                '能够自主生成百万字级的"爽文"小说。',
                '',
                '系统采用五大专业智能体分工协作：',
                '• 编排者：项目管理和流程协调',
                '• 架构师：世界观构建和大纲规划', 
                '• 执笔者：章节内容创作',
                '• 典籍官：知识库管理和检索',
                '• 校订者：质量检查和一致性验证',
              ],
            ),

            // 核心特性
            _buildSection(
              '核心特性',
              Icons.star,
              Colors.orange,
              [
                '🤖 多智能体协作架构',
                '各智能体专业分工，协同工作，确保创作质量',
                '',
                '🧠 分层RAG记忆系统',
                '四层知识库结构：设定层、摘要层、章节层、关系层',
                '',
                '📚 "爽文"创作范式',
                '内置爽文创作规则：黄金三章、爽点分布、角色原型',
                '',
                '🔍 自我批判机制',
                '自动质量检查：逻辑一致性、角色一致性、情节连贯性',
                '',
                '⚡ 高效生成流程',
                '从创意到成文，全自动化创作流程',
              ],
            ),

            // 使用流程
            _buildSection(
              '使用流程',
              Icons.timeline,
              Colors.green,
              [
                '1️⃣ 输入创作提示',
                '描述您想要的小说类型、主角设定、金手指等',
                '',
                '2️⃣ 设置生成参数',
                '选择目标章节数、小说类型等参数',
                '',
                '3️⃣ 启动智能体系统',
                '系统自动协调各智能体开始工作',
                '',
                '4️⃣ 实时监控进度',
                '查看各智能体工作状态和生成进度',
                '',
                '5️⃣ 获取完整作品',
                '下载包含世界观、角色、大纲、章节的完整小说',
              ],
            ),

            // 创作提示示例
            _buildSection(
              '创作提示示例',
              Icons.lightbulb_outline,
              Colors.purple,
              [
                '修仙类：',
                '"一部修仙小说，主角的金手指是一个可以调试物理法则的系统"',
                '',
                '都市异能类：',
                '"一部都市异能小说，主角获得了时间回溯的能力"',
                '',
                '玄幻类：',
                '"一部玄幻小说，主角拥有吞噬万物进化的天赋"',
                '',
                '科幻类：',
                '"一部科幻小说，主角在末世中觉醒了空间异能"',
                '',
                '提示要点：',
                '• 明确小说类型和背景设定',
                '• 描述主角的特殊能力或"金手指"',
                '• 可以包含期望的情节元素',
                '• 保持描述简洁明确',
              ],
            ),

            // 技术说明
            _buildSection(
              '技术说明',
              Icons.settings,
              Colors.teal,
              [
                '🔧 技术架构',
                '• 基于LangChain和LangGraph框架',
                '• 使用ReAct推理模式',
                '• 集成FAISS向量数据库',
                '• 支持多种LLM模型',
                '',
                '💾 数据存储',
                '• 世界观设定：JSON格式存储',
                '• 角色档案：结构化数据',
                '• 章节内容：文本文件',
                '• 知识库：向量化存储',
                '',
                '🔒 质量保证',
                '• 多维度一致性检查',
                '• 自动错误检测和修正',
                '• 内容质量评分',
                '• 人工审核接口',
              ],
            ),

            // 注意事项
            _buildSection(
              '注意事项',
              Icons.warning_amber,
              Colors.red,
              [
                '⚠️ 系统要求',
                '• 需要稳定的网络连接',
                '• 需要在设置中配置AI模型（支持OpenAI、本地模型等）',
                '• 建议使用GPT-4或其他高质量模型获得最佳效果',
                '',
                '⏱️ 生成时间',
                '• 短篇（5-10章）：约10-30分钟',
                '• 中篇（20-50章）：约1-3小时',
                '• 长篇（100章+）：约5-10小时',
                '',
                '💰 成本考虑',
                '• 生成成本取决于章节数和模型选择',
                '• 建议先用少量章节测试',
                '• 可以分批生成以控制成本',
                '',
                '📝 内容质量',
                '• 生成内容仅供参考和娱乐',
                '• 建议人工审核和编辑',
                '• 注意版权和原创性问题',
              ],
            ),

            const SizedBox(height: 20),

            // 操作按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.arrow_back),
                    label: const Text('返回工具广场'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Get.back();
                      // 这里可以导航到AI智能体页面
                    },
                    icon: const Icon(Icons.auto_awesome),
                    label: const Text('开始创作'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple.shade600,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    String title,
    IconData icon,
    Color color,
    List<String> content,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...content.map((text) {
              if (text.isEmpty) {
                return const SizedBox(height: 8);
              }
              return Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Text(
                  text,
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.4,
                  ),
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }
}
