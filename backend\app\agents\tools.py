"""
智能体工具集 - 为各个智能体提供专用工具
"""
import os
import json
from typing import Dict, Any, List, Optional
from langchain.tools import BaseTool
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)

class FileSystemTool(BaseTool):
    """文件系统工具 - 用于读写文件"""
    name = "file_system"
    description = "用于读取和写入文件的工具"
    
    def _run(self, action: str, filename: str, content: str = None) -> str:
        """执行文件操作"""
        try:
            if action == "read":
                if os.path.exists(filename):
                    with open(filename, 'r', encoding='utf-8') as f:
                        return f.read()
                else:
                    return f"文件 {filename} 不存在"
            
            elif action == "write":
                if content is None:
                    return "写入操作需要提供内容"
                
                # 确保目录存在
                os.makedirs(os.path.dirname(filename), exist_ok=True)
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                return f"成功写入文件 {filename}"
            
            elif action == "append":
                if content is None:
                    return "追加操作需要提供内容"
                
                with open(filename, 'a', encoding='utf-8') as f:
                    f.write(content)
                return f"成功追加内容到文件 {filename}"
            
            else:
                return f"不支持的操作: {action}"
                
        except Exception as e:
            logger.error(f"文件操作失败: {str(e)}")
            return f"文件操作失败: {str(e)}"

class JSONTool(BaseTool):
    """JSON工具 - 用于处理JSON数据"""
    name = "json_tool"
    description = "用于创建、读取和修改JSON文件的工具"
    
    def _run(self, action: str, filename: str, data: Dict[str, Any] = None, key: str = None, value: Any = None) -> str:
        """执行JSON操作"""
        try:
            if action == "create":
                if data is None:
                    return "创建JSON需要提供数据"
                
                os.makedirs(os.path.dirname(filename), exist_ok=True)
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                return f"成功创建JSON文件 {filename}"
            
            elif action == "read":
                if os.path.exists(filename):
                    with open(filename, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    return json.dumps(data, ensure_ascii=False, indent=2)
                else:
                    return f"JSON文件 {filename} 不存在"
            
            elif action == "update":
                if not os.path.exists(filename):
                    return f"JSON文件 {filename} 不存在"
                
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if key and value is not None:
                    data[key] = value
                elif data and isinstance(data, dict):
                    data.update(data)
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                return f"成功更新JSON文件 {filename}"
            
            else:
                return f"不支持的JSON操作: {action}"
                
        except Exception as e:
            logger.error(f"JSON操作失败: {str(e)}")
            return f"JSON操作失败: {str(e)}"

class TextGenerationTool(BaseTool):
    """文本生成工具 - 用于生成各种类型的文本内容"""
    name = "text_generation"
    description = "用于生成小说章节、摘要、设定等文本内容的工具"
    
    def __init__(self, llm):
        super().__init__()
        self.llm = llm
    
    def _run(self, task_type: str, prompt: str, context: str = None, max_length: int = 4000) -> str:
        """执行文本生成任务"""
        try:
            full_prompt = prompt
            if context:
                full_prompt = f"上下文信息:\n{context}\n\n任务:\n{prompt}"
            
            response = self.llm.invoke(full_prompt)
            return response.content
            
        except Exception as e:
            logger.error(f"文本生成失败: {str(e)}")
            return f"文本生成失败: {str(e)}"

class SummaryTool(BaseTool):
    """摘要工具 - 用于生成章节摘要"""
    name = "summary_tool"
    description = "用于生成章节摘要和内容总结的工具"
    
    def __init__(self, llm):
        super().__init__()
        self.llm = llm
    
    def _run(self, content: str, summary_type: str = "chapter") -> str:
        """生成摘要"""
        try:
            if summary_type == "chapter":
                prompt = f"""
请为以下章节内容生成一个简洁的摘要，包含：
1. 主要情节发展
2. 关键角色行动
3. 重要对话或事件
4. 章节结尾状态

章节内容：
{content}

摘要：
"""
            else:
                prompt = f"请为以下内容生成摘要：\n{content}"
            
            response = self.llm.invoke(prompt)
            return response.content
            
        except Exception as e:
            logger.error(f"摘要生成失败: {str(e)}")
            return f"摘要生成失败: {str(e)}"

class ValidationTool(BaseTool):
    """验证工具 - 用于检查内容一致性"""
    name = "validation_tool"
    description = "用于验证内容逻辑一致性和设定符合性的工具"
    
    def __init__(self, llm):
        super().__init__()
        self.llm = llm
    
    def _run(self, content: str, reference: str, check_type: str = "consistency") -> str:
        """执行验证检查"""
        try:
            if check_type == "consistency":
                prompt = f"""
请检查以下内容是否与参考设定保持一致：

参考设定：
{reference}

待检查内容：
{content}

请分析：
1. 是否存在逻辑矛盾
2. 角色行为是否符合设定
3. 世界观是否一致
4. 情节发展是否合理

检查结果：
"""
            else:
                prompt = f"请检查以下内容：\n{content}\n\n参考：\n{reference}"
            
            response = self.llm.invoke(prompt)
            return response.content
            
        except Exception as e:
            logger.error(f"验证检查失败: {str(e)}")
            return f"验证检查失败: {str(e)}"

def get_common_tools(llm) -> List[BaseTool]:
    """获取通用工具集"""
    return [
        FileSystemTool(),
        JSONTool(),
        TextGenerationTool(llm),
        SummaryTool(llm),
        ValidationTool(llm)
    ]
