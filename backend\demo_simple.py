#!/usr/bin/env python3
"""
简化的AI小说生成系统演示
不依赖复杂的向量数据库，使用基本的文件存储
"""
import os
import json
import logging
from typing import Dict, Any
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleNovelGenerator:
    """简化的小说生成器"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("需要设置OPENAI_API_KEY环境变量")
        
        # 创建数据目录
        os.makedirs("data", exist_ok=True)
        os.makedirs("data/chapters", exist_ok=True)
        
        logger.info("简化小说生成器初始化完成")
    
    def generate_world_bible(self, user_prompt: str) -> Dict[str, Any]:
        """生成世界观设定"""
        logger.info("开始生成世界观设定...")
        
        # 模拟世界观生成
        world_bible = {
            "world_name": "修仙大陆",
            "genre": "修仙",
            "power_system": {
                "name": "修真体系",
                "levels": [
                    "练气期", "筑基期", "金丹期", "元婴期", 
                    "化神期", "炼虚期", "合体期", "大乘期", "渡劫期"
                ]
            },
            "golden_finger": {
                "name": "物理法则调试系统",
                "description": "可以微调物理法则，提升修炼效率",
                "limitations": "每日使用次数有限，消耗精神力"
            },
            "main_forces": [
                "天剑宗", "万药谷", "血魔教", "散修联盟", "皇室"
            ],
            "important_locations": [
                "天剑峰", "万药山", "血魔窟", "修仙坊市", "皇都"
            ],
            "user_prompt": user_prompt,
            "created_at": datetime.now().isoformat()
        }
        
        # 保存世界观文件
        with open("data/world_bible.json", 'w', encoding='utf-8') as f:
            json.dump(world_bible, f, ensure_ascii=False, indent=2)
        
        logger.info("世界观设定生成完成")
        return world_bible
    
    def generate_characters(self, world_bible: Dict[str, Any]) -> Dict[str, Any]:
        """生成角色档案"""
        logger.info("开始生成角色档案...")
        
        characters = {
            "protagonist": {
                "name": "林天",
                "age": 18,
                "background": "小镇少年，意外获得物理法则调试系统",
                "personality": "坚韧不拔，聪明机智，重情重义",
                "initial_level": "练气期一层",
                "golden_finger": "物理法则调试系统"
            },
            "supporting_characters": [
                {
                    "name": "苏雨",
                    "role": "青梅竹马",
                    "background": "天剑宗弟子，天赋异禀"
                },
                {
                    "name": "老药师",
                    "role": "导师",
                    "background": "隐居的炼丹大师"
                }
            ],
            "antagonists": [
                {
                    "name": "赵公子",
                    "type": "傲慢少爷",
                    "background": "天剑宗长老之子，看不起散修"
                },
                {
                    "name": "血魔老祖",
                    "type": "邪恶强者",
                    "background": "血魔教教主，修为深不可测"
                }
            ],
            "created_at": datetime.now().isoformat()
        }
        
        # 保存角色文件
        with open("data/characters.json", 'w', encoding='utf-8') as f:
            json.dump(characters, f, ensure_ascii=False, indent=2)
        
        logger.info("角色档案生成完成")
        return characters
    
    def generate_outline(self, target_chapters: int) -> Dict[str, Any]:
        """生成情节大纲"""
        logger.info(f"开始生成{target_chapters}章的情节大纲...")
        
        outline = {
            "total_chapters": target_chapters,
            "golden_three_chapters": {
                "chapter_1": "林天在小镇受欺负，意外激活神秘系统",
                "chapter_2": "系统显威，林天初尝修炼甜头，遇到赵公子挑衅",
                "chapter_3": "首次使用系统能力，在众人面前打脸赵公子"
            },
            "main_plot_points": [
                "获得系统，开始修炼",
                "进入天剑宗，遇到各种挑战",
                "系统升级，实力大增",
                "与血魔教对抗",
                "最终成为修仙界传奇"
            ],
            "shuang_points": [
                {"chapter": 3, "type": "打脸", "description": "初次打脸赵公子"},
                {"chapter": 7, "type": "突破", "description": "突破到筑基期"},
                {"chapter": 12, "type": "复仇", "description": "报仇雪恨"},
                {"chapter": 18, "type": "装逼", "description": "震惊全宗"}
            ],
            "chapter_outlines": []
        }
        
        # 生成每章大纲
        for i in range(1, target_chapters + 1):
            if i <= 3:
                # 黄金三章
                chapter_outline = outline["golden_three_chapters"][f"chapter_{i}"]
            elif i <= 10:
                chapter_outline = f"第{i}章：在天剑宗的修炼生活，遇到各种挑战和机遇"
            elif i <= 20:
                chapter_outline = f"第{i}章：实力提升，参与宗门任务，结识更多朋友和敌人"
            else:
                chapter_outline = f"第{i}章：继续主线剧情发展，面对更强大的敌人"
            
            outline["chapter_outlines"].append({
                "chapter": i,
                "title": f"第{i}章",
                "outline": chapter_outline
            })
        
        # 保存大纲文件
        with open("data/outline.json", 'w', encoding='utf-8') as f:
            json.dump(outline, f, ensure_ascii=False, indent=2)
        
        logger.info("情节大纲生成完成")
        return outline
    
    def write_chapter(self, chapter_number: int, outline: Dict[str, Any]) -> str:
        """撰写章节"""
        logger.info(f"开始撰写第{chapter_number}章...")
        
        # 获取章节大纲
        chapter_outline = ""
        for ch in outline["chapter_outlines"]:
            if ch["chapter"] == chapter_number:
                chapter_outline = ch["outline"]
                break
        
        # 模拟章节内容生成
        if chapter_number == 1:
            content = """
林天站在小镇的石桥上，看着远方的群山，心中满怀不甘。

"又是这些人！"他咬牙切齿地看着不远处的几个纨绔子弟。

这些人仗着家里有几个钱，总是欺负他这样的平民子弟。今天更是过分，竟然当众羞辱他。

"林天，你这个废物，也配在这里？"为首的胖子赵胖子嘲笑道。

林天握紧拳头，但他知道自己打不过这些人。就在这时，他的脑海中突然响起一个机械的声音：

"物理法则调试系统启动中..."
"检测到宿主情绪波动，系统激活成功！"
"新手大礼包已发放，请查收！"

林天愣住了，这是什么情况？

系统的声音继续响起："宿主可通过调试物理法则来提升自身能力，当前可用功能：重力调节。"

林天心中一动，难道这就是传说中的金手指？

他悄悄尝试了一下，果然感觉身体变轻了许多。

"看来，我的命运要改变了。"林天眼中闪过一丝精光。
"""
        elif chapter_number == 2:
            content = """
第二天，林天来到了镇上的修炼场。

昨晚他已经初步了解了系统的功能，这个物理法则调试系统简直太神奇了。不仅可以调节重力，还能微调其他物理参数。

"小子，你也来修炼？"一个熟悉的声音传来。

林天转头一看，正是昨天的赵胖子，不过今天他身边还跟着一个更加嚣张的年轻人。

"赵公子，就是这个废物，昨天还敢瞪我。"赵胖子指着林天说道。

那个被称为赵公子的年轻人打量着林天，眼中满是不屑："就这样的废物，也配修炼？"

林天没有说话，而是默默走向修炼器械。

"等等！"赵公子叫住了他，"既然来了修炼场，不如我们比试一下？"

周围的人都围了过来，显然对这场比试很感兴趣。

林天知道，这是系统给他的第一次机会。

"好，我接受。"他平静地说道。

赵公子冷笑："那就比举重吧，看谁能举起更重的东西。"

林天点头同意，心中默默启动了系统的重力调节功能...
"""
        elif chapter_number == 3:
            content = """
修炼场上，所有人都屏息以待。

赵公子先上场，他轻松举起了一百斤的石锁，引来一片叫好声。

"林天，该你了！"赵公子得意地说道，"不过我劝你还是算了，免得丢人现眼。"

林天走向石锁，心中默念："系统，调节重力至50%。"

"重力调节启动，当前重力：50%标准重力。"系统的声音在脑海中响起。

林天感觉身体前所未有的轻盈，他轻松地举起了一百斤的石锁，然后又加了五十斤。

"什么？！"赵公子瞪大了眼睛。

但这还没完，林天继续加重，最终举起了两百斤的重量！

全场哗然！

"这怎么可能？昨天他还是个废物！"

"难道他一直在隐藏实力？"

赵公子的脸色变得铁青："你...你作弊！"

林天放下石锁，淡淡地说道："愿赌服输，赵公子不会连这点气度都没有吧？"

这一刻，林天感受到了前所未有的快感。这就是打脸的感觉吗？

系统的提示音再次响起："恭喜宿主完成首次打脸，获得经验值100点！"

林天嘴角微微上扬，他的逆袭之路，正式开始了！
"""
        else:
            content = f"""
第{chapter_number}章

{chapter_outline}

林天的修炼之路继续前进着。自从获得了物理法则调试系统后，他的实力突飞猛进。

今天，他又面临着新的挑战...

（这里是第{chapter_number}章的详细内容，包含了精彩的情节发展、角色对话和爽点设计）

随着情节的推进，林天的实力越来越强，敌人也越来越强大。但有了系统的帮助，他总能在关键时刻逆转局势。

这就是属于他的传奇故事！
"""
        
        # 保存章节文件
        filename = f"data/chapters/chapter_{chapter_number:03d}.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"第{chapter_number}章\n\n{content.strip()}")
        
        logger.info(f"第{chapter_number}章撰写完成")
        return content.strip()
    
    def generate_novel(self, user_prompt: str, target_chapters: int = 5) -> Dict[str, Any]:
        """生成完整小说"""
        try:
            logger.info(f"开始生成小说，目标章节数：{target_chapters}")
            
            # 1. 生成世界观
            world_bible = self.generate_world_bible(user_prompt)
            
            # 2. 生成角色
            characters = self.generate_characters(world_bible)
            
            # 3. 生成大纲
            outline = self.generate_outline(target_chapters)
            
            # 4. 生成章节
            chapters = []
            for i in range(1, target_chapters + 1):
                content = self.write_chapter(i, outline)
                chapters.append({
                    "chapter": i,
                    "content": content,
                    "word_count": len(content)
                })
            
            # 5. 生成结果报告
            total_words = sum(ch["word_count"] for ch in chapters)
            
            result = {
                "success": True,
                "completed_chapters": target_chapters,
                "target_chapters": target_chapters,
                "total_words": total_words,
                "world_bible": world_bible,
                "characters": characters,
                "outline": outline,
                "chapters": chapters,
                "generated_at": datetime.now().isoformat()
            }
            
            logger.info(f"小说生成完成！共{target_chapters}章，{total_words}字")
            return result
            
        except Exception as e:
            logger.error(f"小说生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "completed_chapters": 0,
                "target_chapters": target_chapters
            }

def main():
    """演示主函数"""
    print("=" * 60)
    print("岱宗AI自主创作智能体系统 - 简化演示版")
    print("=" * 60)
    
    # 检查API密钥
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("注意：未设置OPENAI_API_KEY，使用模拟数据演示")
    
    try:
        # 初始化生成器
        generator = SimpleNovelGenerator()
        
        # 生成小说
        user_prompt = "一部修仙小说，主角的金手指是一个可以调试物理法则的系统"
        target_chapters = 3  # 演示用，只生成3章
        
        print(f"创作提示: {user_prompt}")
        print(f"目标章节: {target_chapters}")
        print("-" * 60)
        
        result = generator.generate_novel(user_prompt, target_chapters)
        
        if result["success"]:
            print("✅ 生成成功！")
            print(f"完成章节: {result['completed_chapters']}")
            print(f"总字数: {result['total_words']}")
            print(f"生成文件:")
            print(f"  - 世界观: data/world_bible.json")
            print(f"  - 角色: data/characters.json")
            print(f"  - 大纲: data/outline.json")
            print(f"  - 章节: data/chapters/")
            
            # 显示第一章内容预览
            if result["chapters"]:
                first_chapter = result["chapters"][0]["content"]
                print(f"\n第一章预览:")
                print("-" * 40)
                print(first_chapter[:200] + "..." if len(first_chapter) > 200 else first_chapter)
                print("-" * 40)
        else:
            print("❌ 生成失败")
            print(f"错误: {result.get('error', '未知错误')}")
        
    except Exception as e:
        print(f"❌ 系统错误: {str(e)}")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
