import 'package:flutter_test/flutter_test.dart';
import 'package:novel_app/config/storage_config.dart';
import 'package:novel_app/models/memory_chunk.dart';

void main() {
  group('Storage Configuration Tests', () {
    test('should validate content correctly', () {
      // 测试有效内容
      expect(StorageConfig.isValidContent('这是一个有效的测试内容'), true);
      
      // 测试无效内容
      expect(StorageConfig.isValidContent(''), false);
      expect(StorageConfig.isValidContent('   '), false);
      expect(StorageConfig.isValidContent('短'), false);
      
      // 测试过长内容
      final longContent = 'a' * (StorageConfig.maxContentLength + 1);
      expect(StorageConfig.isValidContent(longContent), false);
    });

    test('should validate embedding correctly', () {
      // 测试有效嵌入向量
      final validEmbedding = List<double>.filled(384, 0.5);
      expect(StorageConfig.isValidEmbedding(validEmbedding), true);
      
      // 测试无效嵌入向量
      expect(StorageConfig.isValidEmbedding([]), false);
      expect(StorageConfig.isValidEmbedding(List<double>.filled(50, 0.5)), false);
      expect(StorageConfig.isValidEmbedding(List<double>.filled(384, 0.0)), false);
    });

    test('should calculate health score correctly', () {
      expect(StorageConfig.calculateHealthScore(100, 0), 1.0);
      expect(StorageConfig.calculateHealthScore(100, 20), 0.8);
      expect(StorageConfig.calculateHealthScore(0, 0), 1.0);
    });

    test('should determine retry delay correctly', () {
      expect(StorageConfig.getRetryDelay(1), Duration(milliseconds: 500));
      expect(StorageConfig.getRetryDelay(2), Duration(milliseconds: 1000));
      expect(StorageConfig.getRetryDelay(3), Duration(milliseconds: 1500));
    });
  });

  group('Storage Exception Tests', () {
    test('should create storage exception correctly', () {
      final exception = StorageException(
        '测试错误',
        type: StorageErrorType.embeddingGeneration,
      );
      
      expect(exception.message, '测试错误');
      expect(exception.type, StorageErrorType.embeddingGeneration);
      expect(exception.toString(), contains('StorageException'));
    });
  });

  group('Storage Result Tests', () {
    test('should create success result correctly', () {
      final result = StorageResult.success('test-id');
      
      expect(result.success, true);
      expect(result.data, 'test-id');
      expect(result.error, null);
      expect(result.errorType, null);
    });

    test('should create failure result correctly', () {
      final result = StorageResult.failure(
        '测试错误',
        errorType: StorageErrorType.databaseWrite,
      );
      
      expect(result.success, false);
      expect(result.data, null);
      expect(result.error, '测试错误');
      expect(result.errorType, StorageErrorType.databaseWrite);
    });
  });

  group('Memory Storage Stats Tests', () {
    test('should create stats from map correctly', () {
      final map = {
        'totalChunks': 100,
        'validChunks': 90,
        'invalidChunks': 10,
        'healthScore': 0.9,
        'typeDistribution': {'worldSetting': 10, 'character': 20},
        'importanceDistribution': {'critical': 5, 'important': 15},
        'averageContentLength': 1500.0,
        'lastUpdated': '2024-01-01T00:00:00.000Z',
      };
      
      final stats = MemoryStorageStats.fromMap(map);
      
      expect(stats.totalChunks, 100);
      expect(stats.validChunks, 90);
      expect(stats.invalidChunks, 10);
      expect(stats.healthScore, 0.9);
      expect(stats.typeDistribution['worldSetting'], 10);
      expect(stats.importanceDistribution['critical'], 5);
      expect(stats.averageContentLength, 1500.0);
    });

    test('should convert stats to map correctly', () {
      final stats = MemoryStorageStats(
        totalChunks: 100,
        validChunks: 90,
        invalidChunks: 10,
        healthScore: 0.9,
        typeDistribution: {'worldSetting': 10},
        importanceDistribution: {'critical': 5},
        averageContentLength: 1500.0,
        lastUpdated: DateTime.parse('2024-01-01T00:00:00.000Z'),
      );
      
      final map = stats.toMap();
      
      expect(map['totalChunks'], 100);
      expect(map['validChunks'], 90);
      expect(map['invalidChunks'], 10);
      expect(map['healthScore'], 0.9);
      expect(map['typeDistribution']['worldSetting'], 10);
      expect(map['importanceDistribution']['critical'], 5);
      expect(map['averageContentLength'], 1500.0);
      expect(map['lastUpdated'], '2024-01-01T00:00:00.000Z');
    });
  });
}
