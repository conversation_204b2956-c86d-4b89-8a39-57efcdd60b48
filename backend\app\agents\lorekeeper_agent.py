"""
典籍官智能体 - 记忆与RAG管理者
"""
from typing import Dict, Any, List, Optional
from langchain.prompts import PromptTemplate
from langchain_community.vectorstores import FAISS
from langchain_openai import OpenAIEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.docstore.document import Document
from .base_agent import BaseAgent
from .tools import get_common_tools
import json
import os
import logging

logger = logging.getLogger(__name__)

class LorekeeperAgent(BaseAgent):
    """典籍官智能体 - 系统的图书馆员和记忆核心"""
    
    def __init__(self, llm=None):
        super().__init__(
            name="典籍官",
            description="系统的图书馆员和记忆核心，负责管理小说的整个知识库",
            llm=llm,
            tools=get_common_tools(llm) if llm else []
        )
        
        # 初始化向量数据库
        self.embeddings = OpenAIEmbeddings()
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            separators=["\n\n", "\n", "。", "！", "？", "；", " ", ""]
        )
        
        # 分层知识库
        self.knowledge_layers = {
            "settings": None,      # 基础设定文档层
            "summaries": None,     # 章节摘要层
            "chapters": None,      # 完整章节文本层
            "entities": None       # 实体与关系图谱层
        }
        
        self._initialize_knowledge_base()
    
    def _get_system_prompt(self) -> str:
        return """
你是典籍官智能体，负责管理小说创作的全部知识库。你的职责包括：

1. 知识库管理：维护分层的RAG系统
2. 内容索引：对新生成的章节进行分块和向量化
3. 检索服务：为其他智能体提供上下文信息
4. 一致性维护：确保知识库的准确性和完整性

分层知识库结构：
- 第一层：基础设定文档（世界观、角色、大纲）
- 第二层：章节摘要（快速访问层）
- 第三层：完整章节文本（语义分块）
- 第四层：实体与关系图谱（人物、地点、物品关系）

检索策略：
- 混合搜索：结合语义搜索和关键词搜索
- 查询重写：优化搜索查询
- 重排序：提高检索结果相关性

你不参与创意生成，专注于知识管理和检索服务。
"""
    
    def _get_prompt_template(self) -> PromptTemplate:
        return PromptTemplate(
            input_variables=["input", "chat_history", "agent_scratchpad"],
            template="""
{system_prompt}

你有以下工具可以使用：
{tools}

检索请求：{input}

聊天历史：{chat_history}

使用以下格式进行推理和行动：

思考：我需要分析检索请求并确定最佳检索策略
行动：[选择一个工具]
行动输入：[工具的输入参数]
观察：[工具的输出结果]
... (可以重复思考/行动/观察)
思考：我现在找到了相关信息
最终答案：[检索到的相关信息]

开始！

{agent_scratchpad}
""".format(
                system_prompt=self._get_system_prompt(),
                tools="{tools}",
                input="{input}",
                chat_history="{chat_history}",
                agent_scratchpad="{agent_scratchpad}"
            )
        )
    
    def _direct_action(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """直接行动方法"""
        task_type = plan.get("task_type", "unknown")
        
        if task_type == "index_content":
            return self._index_content(plan)
        elif task_type == "search":
            return self._search(plan)
        elif task_type == "update_memory":
            return self._update_memory(plan)
        elif task_type == "get_context":
            return self._get_context(plan)
        else:
            return {"success": False, "error": f"未知任务类型: {task_type}"}
    
    def _initialize_knowledge_base(self):
        """初始化知识库"""
        try:
            # 创建必要的目录
            os.makedirs("data/vectorstore", exist_ok=True)
            os.makedirs("data/summaries", exist_ok=True)
            
            # 尝试加载现有的向量数据库
            for layer in self.knowledge_layers:
                vectorstore_path = f"data/vectorstore/{layer}"
                if os.path.exists(vectorstore_path):
                    try:
                        self.knowledge_layers[layer] = FAISS.load_local(
                            vectorstore_path, self.embeddings
                        )
                        logger.info(f"加载现有{layer}知识库")
                    except:
                        logger.warning(f"无法加载{layer}知识库，将创建新的")
            
            logger.info("知识库初始化完成")
            
        except Exception as e:
            logger.error(f"知识库初始化失败: {str(e)}")
    
    def _index_content(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """索引新内容到知识库"""
        try:
            content_type = plan.get("content_type", "chapter")
            content = plan.get("content", "")
            metadata = plan.get("metadata", {})
            
            if not content:
                return {"success": False, "error": "内容为空"}
            
            # 根据内容类型选择处理方式
            if content_type == "settings":
                return self._index_settings(content, metadata)
            elif content_type == "chapter":
                return self._index_chapter(content, metadata)
            elif content_type == "summary":
                return self._index_summary(content, metadata)
            else:
                return {"success": False, "error": f"不支持的内容类型: {content_type}"}
            
        except Exception as e:
            logger.error(f"内容索引失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _index_settings(self, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """索引设定文档"""
        try:
            # 创建文档
            doc = Document(page_content=content, metadata=metadata)
            
            # 添加到设定层
            if self.knowledge_layers["settings"] is None:
                self.knowledge_layers["settings"] = FAISS.from_documents(
                    [doc], self.embeddings
                )
            else:
                self.knowledge_layers["settings"].add_documents([doc])
            
            # 保存向量数据库
            self.knowledge_layers["settings"].save_local("data/vectorstore/settings")
            
            logger.info("设定文档索引完成")
            return {"success": True, "message": "设定文档索引完成"}
            
        except Exception as e:
            logger.error(f"设定文档索引失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _index_chapter(self, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """索引章节内容"""
        try:
            # 语义分块
            chunks = self.text_splitter.split_text(content)
            
            # 创建文档
            docs = []
            for i, chunk in enumerate(chunks):
                chunk_metadata = metadata.copy()
                chunk_metadata["chunk_id"] = i
                docs.append(Document(page_content=chunk, metadata=chunk_metadata))
            
            # 添加到章节层
            if self.knowledge_layers["chapters"] is None:
                self.knowledge_layers["chapters"] = FAISS.from_documents(
                    docs, self.embeddings
                )
            else:
                self.knowledge_layers["chapters"].add_documents(docs)
            
            # 保存向量数据库
            self.knowledge_layers["chapters"].save_local("data/vectorstore/chapters")
            
            # 生成章节摘要
            summary = self._generate_chapter_summary(content, metadata)
            if summary:
                self._index_summary(summary, metadata)
            
            logger.info(f"章节内容索引完成，共{len(chunks)}个分块")
            return {
                "success": True, 
                "message": f"章节内容索引完成，共{len(chunks)}个分块",
                "chunks_count": len(chunks)
            }
            
        except Exception as e:
            logger.error(f"章节内容索引失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _index_summary(self, content: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """索引摘要内容"""
        try:
            # 创建文档
            doc = Document(page_content=content, metadata=metadata)
            
            # 添加到摘要层
            if self.knowledge_layers["summaries"] is None:
                self.knowledge_layers["summaries"] = FAISS.from_documents(
                    [doc], self.embeddings
                )
            else:
                self.knowledge_layers["summaries"].add_documents([doc])
            
            # 保存向量数据库
            self.knowledge_layers["summaries"].save_local("data/vectorstore/summaries")
            
            # 保存摘要文件
            if "chapter_number" in metadata:
                summary_file = f"data/summaries/chapter_{metadata['chapter_number']:03d}_summary.txt"
                with open(summary_file, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            logger.info("摘要内容索引完成")
            return {"success": True, "message": "摘要内容索引完成"}
            
        except Exception as e:
            logger.error(f"摘要内容索引失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _generate_chapter_summary(self, content: str, metadata: Dict[str, Any]) -> Optional[str]:
        """生成章节摘要"""
        try:
            summary_prompt = f"""
请为以下章节内容生成一个简洁的摘要，包含：
1. 主要情节发展
2. 关键角色行动
3. 重要对话或事件
4. 章节结尾状态

章节内容：
{content}

摘要：
"""
            
            response = self.llm.invoke(summary_prompt)
            return response.content
            
        except Exception as e:
            logger.error(f"章节摘要生成失败: {str(e)}")
            return None
    
    def _search(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """执行搜索"""
        try:
            query = plan.get("query", "")
            search_layers = plan.get("layers", ["summaries", "chapters"])
            top_k = plan.get("top_k", 5)
            
            if not query:
                return {"success": False, "error": "搜索查询为空"}
            
            # 查询重写
            enhanced_query = self._enhance_query(query)
            
            # 在指定层级搜索
            all_results = []
            for layer in search_layers:
                if self.knowledge_layers[layer] is not None:
                    results = self.knowledge_layers[layer].similarity_search(
                        enhanced_query, k=top_k
                    )
                    for result in results:
                        all_results.append({
                            "layer": layer,
                            "content": result.page_content,
                            "metadata": result.metadata
                        })
            
            # 重排序（简单实现）
            ranked_results = self._rerank_results(all_results, query)
            
            logger.info(f"搜索完成，找到{len(ranked_results)}个结果")
            return {
                "success": True,
                "query": query,
                "enhanced_query": enhanced_query,
                "results": ranked_results[:top_k]
            }
            
        except Exception as e:
            logger.error(f"搜索失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _enhance_query(self, query: str) -> str:
        """增强搜索查询"""
        try:
            enhance_prompt = f"""
请将以下搜索查询扩展为更详细、更有效的检索查询：

原始查询：{query}

扩展后的查询应该：
1. 包含相关的关键词
2. 考虑可能的同义词
3. 明确搜索意图

扩展查询：
"""
            
            response = self.llm.invoke(enhance_prompt)
            return response.content.strip()
            
        except Exception as e:
            logger.warning(f"查询增强失败: {str(e)}")
            return query
    
    def _rerank_results(self, results: List[Dict], query: str) -> List[Dict]:
        """重排序搜索结果"""
        # 简单的重排序实现，可以后续优化
        return sorted(results, key=lambda x: len(x["content"]), reverse=True)
    
    def _update_memory(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """更新记忆"""
        try:
            chapter_number = plan.get("chapter_number", 0)
            chapter_content = plan.get("chapter_content", "")
            
            if not chapter_content:
                return {"success": False, "error": "章节内容为空"}
            
            # 索引章节内容
            metadata = {
                "chapter_number": chapter_number,
                "content_type": "chapter",
                "timestamp": str(os.path.getmtime(__file__))
            }
            
            result = self._index_chapter(chapter_content, metadata)
            
            return result
            
        except Exception as e:
            logger.error(f"记忆更新失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _get_context(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """获取上下文信息"""
        try:
            context_type = plan.get("context_type", "general")
            chapter_number = plan.get("chapter_number", 0)
            
            context_parts = []
            
            # 获取相关设定
            if context_type in ["general", "settings"]:
                settings_results = self._search({
                    "query": "世界观 角色 设定",
                    "layers": ["settings"],
                    "top_k": 3
                })
                if settings_results["success"]:
                    context_parts.extend([r["content"] for r in settings_results["results"]])
            
            # 获取近期章节摘要
            if context_type in ["general", "recent"]:
                if chapter_number > 1:
                    recent_query = f"第{max(1, chapter_number-3)}章到第{chapter_number-1}章"
                    recent_results = self._search({
                        "query": recent_query,
                        "layers": ["summaries"],
                        "top_k": 3
                    })
                    if recent_results["success"]:
                        context_parts.extend([r["content"] for r in recent_results["results"]])
            
            return {
                "success": True,
                "context": "\n\n".join(context_parts),
                "context_parts": len(context_parts)
            }
            
        except Exception as e:
            logger.error(f"上下文获取失败: {str(e)}")
            return {"success": False, "error": str(e)}
