"""
AI智能体路由 - 提供小说生成API
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel
from typing import Dict, Any, Optional
import os
import json
import logging
from datetime import datetime

from ..agents.novel_generation_system import NovelGenerationSystem
from ..config.agent_config import get_model_providers, get_provider_models
from ..database import get_db
from ..models import User
from ..auth import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/ai-agents", tags=["AI智能体"])

# 全局系统实例
novel_system: Optional[NovelGenerationSystem] = None

class NovelGenerationRequest(BaseModel):
    """小说生成请求"""
    prompt: str
    target_chapters: int = 100
    genre: str = "修仙"

    # LLM配置
    provider: str = "openai"
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    model: str = "gpt-4-turbo-preview"
    temperature: float = 0.7
    max_tokens: int = 4000

    # 兼容性字段
    openai_api_key: Optional[str] = None  # 向后兼容

class NovelGenerationResponse(BaseModel):
    """小说生成响应"""
    task_id: str
    status: str
    message: str

class GenerationStatus(BaseModel):
    """生成状态"""
    task_id: str
    status: str
    progress: Dict[str, Any]
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

# 存储生成任务状态
generation_tasks: Dict[str, Dict[str, Any]] = {}

def get_novel_system() -> NovelGenerationSystem:
    """获取小说生成系统实例"""
    global novel_system
    if novel_system is None:
        # 从环境变量获取API密钥
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            raise HTTPException(
                status_code=500,
                detail="OpenAI API密钥未配置，请设置OPENAI_API_KEY环境变量"
            )
        novel_system = NovelGenerationSystem(api_key)
    return novel_system

async def generate_novel_task(
    task_id: str,
    request: NovelGenerationRequest
):
    """后台任务：生成小说"""
    try:
        logger.info(f"开始生成任务 {task_id}")
        
        # 更新任务状态
        generation_tasks[task_id]["status"] = "running"
        generation_tasks[task_id]["started_at"] = datetime.now().isoformat()
        
        # 初始化系统
        api_key = request.api_key or request.openai_api_key  # 向后兼容
        if api_key or request.provider != "openai":
            system = NovelGenerationSystem(
                provider=request.provider,
                api_key=api_key,
                base_url=request.base_url,
                model=request.model,
                temperature=request.temperature,
                max_tokens=request.max_tokens
            )
        else:
            system = get_novel_system()
        
        # 生成小说
        result = system.generate_novel(
            user_prompt=request.prompt,
            target_chapters=request.target_chapters
        )
        
        # 更新任务结果
        generation_tasks[task_id]["status"] = "completed" if result["success"] else "failed"
        generation_tasks[task_id]["result"] = result
        generation_tasks[task_id]["completed_at"] = datetime.now().isoformat()
        
        logger.info(f"任务 {task_id} 完成，状态：{generation_tasks[task_id]['status']}")
        
    except Exception as e:
        logger.error(f"任务 {task_id} 失败: {str(e)}")
        generation_tasks[task_id]["status"] = "failed"
        generation_tasks[task_id]["error"] = str(e)
        generation_tasks[task_id]["completed_at"] = datetime.now().isoformat()

@router.post("/generate-novel", response_model=NovelGenerationResponse)
async def start_novel_generation(
    request: NovelGenerationRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """开始小说生成"""
    try:
        # 生成任务ID
        task_id = f"novel_{current_user.id}_{int(datetime.now().timestamp())}"
        
        # 初始化任务状态
        generation_tasks[task_id] = {
            "task_id": task_id,
            "user_id": current_user.id,
            "status": "pending",
            "request": request.model_dump(),
            "created_at": datetime.now().isoformat(),
            "progress": {
                "current_chapter": 0,
                "target_chapters": request.target_chapters,
                "current_phase": "initialization"
            }
        }
        
        # 添加后台任务
        background_tasks.add_task(generate_novel_task, task_id, request)
        
        logger.info(f"用户 {current_user.username} 启动小说生成任务 {task_id}")
        
        return NovelGenerationResponse(
            task_id=task_id,
            status="pending",
            message="小说生成任务已启动，请使用task_id查询进度"
        )
        
    except Exception as e:
        logger.error(f"启动小说生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/generation-status/{task_id}", response_model=GenerationStatus)
async def get_generation_status(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """获取生成状态"""
    try:
        if task_id not in generation_tasks:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        task = generation_tasks[task_id]
        
        # 检查权限
        if task["user_id"] != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此任务")
        
        return GenerationStatus(
            task_id=task_id,
            status=task["status"],
            progress=task.get("progress", {}),
            result=task.get("result"),
            error=task.get("error")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取生成状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/user-tasks")
async def get_user_tasks(
    current_user: User = Depends(get_current_user)
):
    """获取用户的所有任务"""
    try:
        user_tasks = []
        for task_id, task in generation_tasks.items():
            if task["user_id"] == current_user.id:
                user_tasks.append({
                    "task_id": task_id,
                    "status": task["status"],
                    "created_at": task["created_at"],
                    "request": task["request"]
                })
        
        return {"tasks": user_tasks}
        
    except Exception as e:
        logger.error(f"获取用户任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/cancel-task/{task_id}")
async def cancel_generation_task(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """取消生成任务"""
    try:
        if task_id not in generation_tasks:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        task = generation_tasks[task_id]
        
        # 检查权限
        if task["user_id"] != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此任务")
        
        # 只能取消pending或running状态的任务
        if task["status"] in ["pending", "running"]:
            task["status"] = "cancelled"
            task["cancelled_at"] = datetime.now().isoformat()
            return {"message": "任务已取消"}
        else:
            raise HTTPException(status_code=400, detail="任务无法取消")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/download-novel/{task_id}")
async def download_novel(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """下载生成的小说"""
    try:
        if task_id not in generation_tasks:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        task = generation_tasks[task_id]
        
        # 检查权限
        if task["user_id"] != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此任务")
        
        # 检查任务状态
        if task["status"] != "completed":
            raise HTTPException(status_code=400, detail="任务未完成")
        
        result = task.get("result", {})
        if not result.get("success"):
            raise HTTPException(status_code=400, detail="生成失败，无法下载")
        
        # 收集所有章节文件
        chapters = []
        completed_chapters = result.get("completed_chapters", 0)
        
        for i in range(1, completed_chapters + 1):
            chapter_file = f"data/chapters/chapter_{i:03d}.txt"
            if os.path.exists(chapter_file):
                with open(chapter_file, 'r', encoding='utf-8') as f:
                    chapters.append(f.read())
        
        # 组合完整小说
        novel_content = "\n\n".join(chapters)
        
        # 添加元信息
        metadata = {
            "title": f"AI生成小说_{task_id}",
            "generated_at": task.get("completed_at"),
            "chapters": completed_chapters,
            "world_bible": result.get("world_bible"),
            "characters": result.get("characters"),
            "outline": result.get("outline")
        }
        
        return {
            "novel_content": novel_content,
            "metadata": metadata,
            "word_count": len(novel_content)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载小说失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/system-status")
async def get_system_status():
    """获取系统状态"""
    try:
        return {
            "system_initialized": novel_system is not None,
            "active_tasks": len([t for t in generation_tasks.values() if t["status"] == "running"]),
            "total_tasks": len(generation_tasks),
            "openai_configured": bool(os.getenv("OPENAI_API_KEY")),
            "supported_providers": list(get_model_providers().keys())
        }

    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/model-providers")
async def get_available_providers():
    """获取可用的模型提供商"""
    try:
        providers = get_model_providers()
        return {
            "success": True,
            "providers": providers
        }
    except Exception as e:
        logger.error(f"获取模型提供商失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/model-providers/{provider}/models")
async def get_provider_models_api(provider: str):
    """获取指定提供商的模型列表"""
    try:
        models = get_provider_models(provider)
        if not models:
            raise HTTPException(status_code=404, detail=f"提供商 {provider} 不存在")

        return {
            "success": True,
            "provider": provider,
            "models": models
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取提供商模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/test-agents")
async def test_agents(
    current_user: User = Depends(get_current_user)
):
    """测试智能体系统"""
    try:
        system = get_novel_system()
        
        # 简单测试
        test_result = {
            "orchestrator": system.orchestrator.get_status(),
            "architect": system.architect.get_status(),
            "chronicler": system.chronicler.get_status(),
            "lorekeeper": system.lorekeeper.get_status(),
            "editor": system.editor.get_status()
        }
        
        return {
            "message": "智能体系统测试完成",
            "agents": test_result
        }
        
    except Exception as e:
        logger.error(f"智能体测试失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
