
构建自主小说家：用于生成百万字级爽文的多智能体系统蓝图


第一部分：基础架构与核心概念

本部分旨在为整个自主小说创作系统奠定理论与架构的基石。我们将从智能体AI的通用原则出发，逐步构建一个专为长篇叙事而设计的、分层的多智能体架构，并深入探讨作为系统核心的小说记忆系统。

第一节：自主创作的智能体范式


1.1 智能体AI工作流简介

智能体AI工作流（Agentic AI Workflows）是指由AI智能体在极少人类干预下自主做出决策、解决问题并执行任务的系统 1。与遵循严格预设规则的传统自动化工具不同，智能体系统能够适应变化、从经验中学习，并处理不可预测的情况 2。这种自主性是实现全程无人干预小说创作这一目标的技术基石。
所有智能体的运作都遵循一个基本的认知循环：感知 -> 思考 -> 行动 3。这个循环构成了系统中每个智能体行为的基础：
感知 (Perceive): 智能体从其环境中收集信息，这些信息可以来自用户指令、其他智能体的输出，或是本地文件系统中的数据。
思考 (Think): 以大语言模型（LLM）作为推理引擎，智能体分析收集到的信息，权衡不同选项，并制定行动计划。
行动 (Act): 智能体执行选定的动作，例如调用一个工具、向文件写入内容，或向另一个智能体发送消息。

1.2 智能体系统的核心组件

一个功能完备的智能体工作流建立在三大支柱之上 1：
大语言模型 (Large Language Models, LLMs): 作为每个智能体的“大脑”或推理引擎。在模型选择上需要进行权衡，例如，GPT-4 Turbo在复杂推理上表现出色，而Claude 3 Opus则擅长处理长文本上下文 5。
工具 (Tools): 作为智能体的“双手”，是其与外部世界交互的接口。这些工具本质上是智能体可以调用的函数，使其能够执行特定任务，如搜索数据库、进行计算，以及本项目中至关重要的读写文件能力 5。
提示工程 (Prompt Engineering): 这是一门精心设计结构化指令的艺术，旨在引导智能体的行为，确保其行动与总体目标保持一致 1。我们将通过提示工程，将“爽文”的创作规则和叙事范式编码为智能体可以理解和执行的指令。

第二节：用于叙事生成的分层多智能体架构


2.1 采用多智能体系统的基本原理

对于创作百万字级小说这样的复杂任务，单一的、功能庞杂的智能体是不可行的。当一个智能体被赋予过多的工具和过于宽泛的职责时，它会变得容易混淆、效率低下且错误频出 9。因此，通过将复杂的创作流程分解，并将专业化的任务委派给不同的智能体——例如，一个负责情节规划，一个负责章节写作，另一个负责审校——我们可以显著提升系统的清晰度、可扩展性和可靠性 5。
本方案将采用一种**分层编排（Hierarchical Orchestration）**模型 5。在该模型中，一个“管理者”智能体负责指导整个工作流程，并将具体任务分配给一个由多个专业化子智能体组成的团队。这种结构模仿了人类世界中的写作团队（如主笔、编辑、事实核查员等），确保了任务的高效协同。

2.2 智能体团队：角色与职责

本系统将由一个核心智能体团队构成，每个成员都拥有明确定义的角色、指令集和核心工具。这种设计借鉴了现实世界中多智能体系统 4 和AI辅助创意写作工具 10 的成功实践。
编排者 (Orchestrator Agent): 系统的项目经理。它接收用户的初始请求，对整个工作流程进行排序（例如，“第一步，调用‘架构师’构建世界观。第二步，循环调用‘执笔者’和‘校订者’，直到达到指定章节数”），并处理最终的输出。它根据项目的当前状态决定下一步调用哪个智能体 5。
架构师 (Architect Agent - 世界构建与大纲规划): 系统的创意总监。其核心目标是将用户的高层级概念转化为结构化的、机器可读的创作蓝图。
工具: 文本生成、文件输入/输出（用于创建 world_bible.json, characters.json, outline.json 等文件）。
流程: 负责生成详细的世界观设定文档、包含人物成长弧线的角色档案 11，以及一个从主线剧情到各章节概要的分层情节大纲。这个过程类似于RaPID框架中的大纲生成 13 和StoryCraftr项目的功能 14。
执笔者 (Chronicler Agent - 章节作者): 主要的叙事内容生成器。它的唯一职责是高质量地撰写单个章节的正文。
工具: 文本生成、RAG检索（从“典籍官”获取上下文）、文件输入/输出（写入 chapter_N.txt）。
流程: 它从“编排者”处接收指令，如：“根据此章节概要撰写第五章，重点关注角色A的内心独白。” 在动笔前，它会向“典籍官”查询相关上下文（例如，“上一章发生了什么？角色A当前的情绪状态如何？”），以确保内容的连续性。
典籍官 (Lorekeeper Agent - 记忆与RAG管理者): 系统的图书馆员和记忆核心。该智能体不参与创意生成，而是负责管理小说的整个知识库。
工具: 向量数据库交互（索引、语义搜索、过滤）、文件系统监控、文本分块/摘要工具。
流程: 它会持续不断地接收新生成的章节，对其进行分块、创建向量嵌入并存入数据库。它为所有其他智能体提供检索服务，从而提供必要的上下文信息，以克服大语言模型有限的上下文窗口问题 15。
校订者 (Editor Agent - 逻辑与一致性评论家): 系统的质量保证引擎。该智能体体现了“创造者-评论家”（Creator-Critic）模型中的“评论家”角色 4，其核心职责是自主检测并标记出小说中的不一致之处。
工具: RAG检索（用于事实核查）、逻辑比较工具。
流程: 在一个章节被撰写完成后，“编排者”会指派“校订者”进行审查。“校订者”会执行一系列检查，例如：“本章对X城的描述是否与世界观设定一致？”或“角色B的能力成长是否符合逻辑，并与前文保持一致？”它会使用受SELF-RAG启发的自反思技术来评估叙事 15。这一过程并非简单地判断对错，而是将“保持一致性”这一高级目标，转化为一系列具体、可验证的数据库查询。例如，在审查第50章时，它的内部思考（由ReAct框架驱动）可能是：首先，思考需要验证角色C在本章中陈述的动机是否与其背景故事相符；然后，行动，向“典籍官”发出查询，请求检索角色C的背景故事以及第50章中所有相关对话；最后，基于观察到的信息进行自我批判，判断动机是否得到背景故事的支持。这种机制将“校订者”从一个被动的校对工具，提升为一个主动的、智能的质量保证系统。

第三节：记忆核心：长篇叙事的分层RAG策略


3.1 规模的挑战：超越朴素RAG

对于百万字级别的小说，标准的、仅检索扁平文本块的朴素RAG（Retrieval-Augmented Generation）是远远不够的。这种方法会丢失宏观的叙事结构和上下文联系，导致前后脱节 15。正如在学术和长篇文本分析领域所遇到的挑战一样，我们需要一种更复杂的策略 13。
解决方案是构建一个**分层RAG（Hierarchical RAG）**系统。这种架构并非凭空设计，而是对故事本身嵌套结构（小说 -> 情节 -> 章节 -> 场景）的直接映射。新兴的图RAG和分层RAG研究（如HiRAG, ArchRAG）也强调了结构在知识检索中的重要性 19。这种AI系统架构与它所创造的叙事作品之间在结构上的同构性，是确保效率和连贯性的根本要求。一个扁平的智能体系统难以管理情节的嵌套依赖，而一个扁平的RAG系统则无法在合适的抽象层次上检索上下文（例如，有时需要情节摘要，有时则需要具体的对话）。

3.2 构建分层知识库

“典籍官”所管理的并非一个扁平的文本块数据库，而是一个多层次、相互关联的知识图谱。这需要一系列先进的预检索和索引策略。
第一层：基础设定文档 (小说的“宪法”): 这是故事不可更改的核心。包括由“架构师”生成的 world_bible.json、characters.json 和初始的高层 outline.json。这些文档作为结构化数据被索引。
第二层：章节摘要: “执笔者”每完成一章，“典籍官”就会利用LLM为其生成一个简洁的摘要。这些摘要构成了一个“快速访问”层，使智能体无需阅读全文即可掌握情节走向。这是一种有效的上下文压缩技术 25。
第三层：完整章节文本 (原始文本): 每个章节的完整文本，采用**语义分块（Semantic Chunking）**技术进行分割 25。与固定大小的分块不同，该方法根据句子的语义相似性进行分组，从而保留了完整的思想单元，避免了在场景或对话中途生硬断开的尴尬。
第四层：实体与关系图谱: “典籍官”会识别文中的关键实体（人物、地点、物品）及其关系，并构建一个知识图谱。这使得系统能够处理复杂的关系查询，例如“在过去十章中，角色A与谁有过互动？”。该设计灵感来源于先进的图RAG技术 22。

3.3 先进的检索与后检索策略

为了确保智能体获得的是最佳而非任意的上下文，我们将实施一个多阶段的检索流程：
检索阶段 - 混合搜索 (Hybrid Search): “典籍官”将结合语义（向量）搜索和关键词搜索。这确保了对特定名称或术语（如“金龙剑”）的查询能够被精确找到，即使它们的语义含义并不独特 25。
检索阶段 - 查询重写 (Query Rewriting): 在执行搜索之前，智能体的原始查询（例如，“角色A现在该做什么？”）将被LLM扩展为一个更有效、更详细的数据库查询（例如，“检索近期事件、角色A的当前目标以及直接的环境威胁，以为其下一步行动提供信息。”）25。
后检索阶段 - 重排序与过滤 (Reranking and Filtering): 初始搜索将“过量获取”（over-fetch）比实际需要更多的文档（例如，返回前20个文本块）。然后，一个更强大但速度较慢的交叉编码器（cross-encoder）模型会对这20个文本块进行相关性重排序，最终只将最相关的顶部5个块传递给请求的智能体。这极大地提高了检索上下文的信噪比 25。

第二部分：实施蓝图与技术深潜

本部分将前述架构转化为具体的实施计划，提供构建该系统所需的技术细节，并重点介绍所选用的框架和关键技术。

第四节：框架与工具：使用LangChain与LangGraph实现架构


4.1 为何选择LangGraph？状态与循环工作流

虽然LangChain非常适合构建简单的顺序链，但我们所提出的多智能体系统需要复杂的、循环的、有状态的交互（例如，写作-编辑-重写的循环）。LangGraph是LangChain的一个扩展，专为此类场景设计，它将工作流表示为有状态的图 27。我们将详细说明如何定义系统的全局状态（例如，
current_chapter, word_count, editor_feedback），以及智能体如何在图中的节点之间转换。

4.2 环境设置与核心组件

本节将提供一个实践性的分步指南。
安装: pip install langchain langgraph langchain_openai faiss-cpu python-dotenv 27。
智能体定义: 提供Python代码片段，展示如何使用LangChain的create_react_agent等函数来定义一个智能体，并将其与LLM和一组工具绑定 29。
工具定义: 展示如何在Python中创建自定义工具。一个关键示例将是FileSystemTool，它允许智能体执行write_file和read_file操作，这对于创建章节文件至关重要。这将借鉴关于自定义工具创建 7 和文件系统交互 8 的示例。
记忆管理: 解释如何为智能体间的对话实现短期记忆，并将其连接到“典籍官”所管理的长期RAG系统 5。

4.3 使用LangGraph构建智能体图

我们将把智能体团队映射到一个LangGraph结构中。
节点 (Nodes): 每个智能体（架构师、执笔者、校订者等）都将成为图中的一个节点。
边 (Edges): “编排者”的逻辑将定义图的边，从而指导控制流。我们将使用**条件边（Conditional Edges）**来处理逻辑判断，例如，“如果‘校订者’发现错误，则将流程路由回‘执笔者’节点；否则，进入‘典籍官’的知识库吸收节点。”
状态管理: 我们将借鉴ResearchState 27 的概念，在Python中定义一个
NovelState TypedDict，用于跟踪整个小说的创作进度。

第五节：认知引擎：实现ReAct与自我修正


5.1 ReAct框架的实际应用

本节将详细阐述实现智能体遵循**推理+行动（Reason+Act）**模式所需的提示工程。我们将提供一个完整的提示模板，强制LLM以结构化的思考 -> 行动 -> 行动输入 -> 观察格式输出其推理过程 30。
示例演练： 以下是“执笔者”智能体的一个ReAct循环追踪：
问题: "撰写第十章。"
思考: "我需要第十章的概要和第九章的总结，以确保内容的连续性。"
行动: Lorekeeper.search
行动输入: {"query": "synopsis for Chapter 10 and summary of Chapter 9"}
观察: (“典籍官”返回了请求的文本)。
思考: "现在我有了上下文。我将开始撰写本章的正文。"
行动: FileSystem.write_file
行动输入: {"filename": "chapter_10.txt", "content": "..."}

5.2 实现校订者的自我批判机制

这是为“校订者”智能体实现SELF-RAG概念的技术方案。我们不会像原始SELF-RAG论文那样从头开始微调一个模型 33，而是通过提示工程和一个多步骤流程来模拟其行为。
通过提示生成反思标签: “校订者”将使用一个特定的提示，要求它在生成分析的同时，附带上“反思标签”。
[IsConsistent]: 本章内容是否与已建立的背景设定相矛盾？ (是/否/部分)
[IsPlausible]: 角色的行为鉴于其性格是否合理？ (是/否)
[FollowsOutline]: 本章是否遵循了“架构师”提供的大纲？ (是/否)
批判流程:
“校订者”接收到新章节的文本及其对应的概要。
它向“典籍官”生成多个验证性查询（如第二节所述）。
它接收到检索到的证据。
它调用LLM，并提供一个包含章节文本、大纲、检索到的证据以及生成带有反思标签的结构化批判的指令的主提示。
结构化的输出（例如，一个包含标签和自然语言解释的JSON对象）被传回给“编排者”。这个过程是SELF-RAG实施中自我批判和评估步骤的实际应用 18。

第六节：工程化机器的“灵魂”：编码“爽文”范式


6.1 将类型惯例转化为智能体指令

本节是连接文学理论与代码实现的桥梁。“爽文”的核心不仅在于情节，更在于节奏和读者满足感。我们将把这些抽象概念编码为具体的规则。
节奏与“爽点” (Shuǎngdiǎn): “架构师”在生成大纲时将遵循一条规则：每隔N章（一个用户可配置的参数）必须出现一个“爽点”（例如，复仇、能力提升、公开打脸）。情节必须紧凑，重大的逆转需在几章内迅速发生，拒绝拖沓冗长的铺垫 36。
主角光环与“金手指” (Jīnshǒuzhǐ): 主角的特殊能力或优势不仅仅是一个情节装置，它是世界的核心规则。架构师必须在world_bible.json中明确定义“金手指”，包括其规则、限制和代价。而“校订者”的职责是确保其使用与这些规则保持一致，防止其沦为懒惰的deus ex machina 36。

6.2 表格：“爽文”范式到智能体指令的映射

这张表格将是本节的核心产物，它为实现提供了一个清晰、可操作的指南。它的价值在于，它将用户抽象的创作目标（“写一部爽文小说”）直接转化为AI智能体的具体、可编程的指令。它揭开了创作过程的神秘面纱，将其转变为一个工程问题，这正是构建成功的自主系统的核心。
“爽文”范式/概念
主要负责智能体
技术实现策略
相关资料
黄金三章
架构师
实施一个专门的、高优先级的提示模板，用于生成前三章的大纲。该模板将强制执行：1. 主角在受压迫/不利状态下登场。2. 迅速引入核心冲突和反派。3. 揭示或首次使用“金手指”。4. 在第三章结束前完成第一个次要“爽点”。
36
“金手指”
架构师, 执笔者, 校订者
架构师: 在world_bible.json中将“金手指”定义为一个结构化对象，包含名称、功能、限制和代价等键。执笔者: 在撰写涉及其使用的场景时，必须向“典籍官”查询此对象的定义。校订者: 通过查询“典籍官”获取“金手指”的所有历史使用记录，并与世界观设定中的规则进行比较，以执行一致性检查。
36
紧凑节奏与“爽点”
架构师, 编排者
架构师: 大纲生成提示中包含约束：“确保每5-10章发生一次重大情节推进或‘爽点’（如复仇、能力提升或羞辱傲慢的对手）。” 编排者: 可以跟踪一个“爽点冷却时间”的状态变量，确保这些事件不会过于集中或间隔太远。
36
主角的逆袭
架构师
主情节大纲必须遵循清晰的“逆袭”弧线。“架构师”将生成一个情节图，其中主角的“能力等级”或“社会地位”是一个可量化的指标，在小说进程中必须呈现强劲的上升趋势，从低谷走向巅峰。
36
脸谱化反派 (工具人配角)
架构师, 执笔者
架构师: 反派角色的设定将使用简化的“打脸”原型：如傲慢的少爷、嫉妒的同辈等。他们的动机应简单且与主角直接对立。执笔者: 在为这些反派撰写对话时，使用强调其傲慢和轻蔑的提示，以最大化其最终被击败时的满足感。
44
逻辑一致性与情节漏洞
校订者, 典籍官
校订者: 定期运行“一致性检查”程序，使用动态查询生成。例如，向“典籍官”查询：“列出所有关于角色A阵营立场的陈述。与第X章中他的行为进行比较。”然后使用类似SELF-RAG的流程评估响应是否存在矛盾。
4


第三部分：操作流程与未来展望

本部分将详细描述系统运行的全过程，并探讨未来的改进方向和潜在挑战。

第七节：端到端的自主生成过程


7.1 从init到complete

本节将通过叙事性演练，模拟智能体间的交互，展示完整的生成流程。
用户输入: 用户运行脚本并提供一个初始指令：python main.py --prompt "一部修仙小说，主角的金手指是一个可以调试物理法则的系统。" --chapters 2000。
编排者 -> 架构师: “编排者”启动LangGraph并调用“架构师”。“架构师”生成基础设定文档并保存。
编排者 -> 典籍官: “典籍官”将这些初始文档吸收到知识库中。
生成循环 (编排者 -> 执笔者 -> 校订者 -> 典籍官): “编排者”进入一个从第1章到第2000章的循环。在每次循环中，它依次调用“执笔者”和“校订者”。如果“校订者”批准，该章节被定稿，“典籍官”将其吸收；否则，带着“校订者”的反馈意见，对当前章节重复此循环。
完成: 当达到指定的章节数时，循环终止。“编排者”输出“小说已完成”的消息。
这个过程展示了一个动态反馈循环，而非简单的线性流水线。系统不仅仅是在执行一个静态计划，而是根据自身在最细粒度上的创作输出（即章节正文）来动态调整后续计划。例如，“执笔者”在第10章中即兴添加的细节，会被“典籍官”吸收，从而影响“校订者”的审查和“执笔者”在撰写第11章时的上下文。这种机制对于在百万字的长篇幅中防止“叙事漂移”至关重要，它是将智能体循环 3 与持续更新的RAG记忆 16 相结合的直接成果。

7.2 生成后交互与修订

在自主运行结束后，系统切换到用户驱动模式。用户现在可以像使用StoryCraftr 14 一样发出指令：
!iterate rewrite-chapter 5 "让主角在最终对决中表现得更冷酷。"
!worldbuilding expand-magic-system "详细描述五大元素魔法流派。"
这些指令由“编排者”解析，并路由到相应的智能体（“执笔者”负责重写，“架构师”负责世界观扩展），展示了系统的灵活性和可交互性 39。

第八节：战略建议与高级考量


8.1 模型选择与微调

专有模型 vs. 开源模型: 讨论两者之间的权衡。像GPT-4或Claude 3这样的专有模型开箱即用，提供顶级的推理能力 5，但在大规模使用时成本高昂且控制力较弱。而本地运行的开源模型（如Llama 3, Mistral）27 则提供了数据隐私和较低的运营成本，但可能需要更多的提示工程或微调。
微调策略: 建议在一个包含大量高质量“爽文”小说的数据集上对基础模型进行微调。微调的目的不是教授模型事实知识，而是向其灌输该特定类型的风格、节奏和词汇，从而减轻提示工程的负担。这与为特定写作目的创建专门模型的研究方向一致 40。

8.2 可扩展性、成本与性能

计算成本: 需要认识到，一次完整的运行将涉及数百万次LLM调用。我们将讨论成本管理策略，例如为简单任务（如章节摘要）使用更便宜、更快的模型，而将最强大的模型保留给复杂的推理任务（如“架构师”和“校订者”）。这是一种“动态调度”策略 40。
性能: 讨论缓存策略以避免冗余的LLM调用（例如，缓存“典籍官”的查询结果），以及使用异步执行来处理智能体的工具调用，以提高系统吞吐量 5。

结论

本报告详细阐述了构建一个能够自主创作百万字级“爽文”小说的多智能体系统的综合蓝图。该系统的核心在于其分层、协同的架构，它不仅是工程上的选择，更是对长篇叙事内在结构的必然反映。通过将复杂的创作任务分解给“编排者”、“架构师”、“执笔者”、“典籍官”和“校订者”等专业化智能体，系统得以在保持逻辑一致性的同时，处理前所未有的文本规模。
该方案的可行性建立在两大关键支柱之上。首先是先进的分层RAG记忆核心，它通过多层次的知识索引（从设定文档到章节摘要再到语义文本块）和复杂的检索策略（混合搜索、查询重写、重排序），有效克服了大语言模型的上下文窗口限制。其次是基于规则的自主批判与修正机制。“爽文”这一文学类型的公式化特性，为AI的自我校对提供了独特的优势。它将主观的“创作质量”转化为客观的、可验证的指标（如能力等级的提升、爽点的固定节奏），使得“校订者”能够进行精确、自动化的逻辑审查。
然而，实现这一宏伟目标仍面临挑战。主要的挑战在于计算成本和性能优化。数百万字的生成意味着海量的API调用，必须采用动态模型调度和高效缓存等策略来控制开销。此外，提示工程的精细度直接决定了最终输出的质量，将“爽文”的微妙之处（如语言风格、情绪张力）精确编码为机器指令，是一项艰巨但至关重要的任务。
总而言之，本报告提出的并非一个遥不可及的幻想，而是一个基于当前AI技术、具有明确实施路径的工程蓝图。通过将智能体工作流、分层RAG和类型化创作规则相结合，我们有望创造出第一个真正意义上的自主“小说家”，这不仅将彻底改变网络文学的生产方式，也将为通用人工智能在复杂、长周期创意任务中的应用开辟新的前沿。
引用的著作
What Are Agentic Workflows? - Salesforce, 访问时间为 六月 24, 2025， https://www.salesforce.com/agentforce/agentic-workflows/
www.atlassian.com, 访问时间为 六月 24, 2025， https://www.atlassian.com/blog/artificial-intelligence/ai-agentic-workflows#:~:text=AI%20agentic%20workflows%20are%20tools,changes%20and%20learn%20from%20experience.
Understanding AI Agentic Workflows | Atlassian, 访问时间为 六月 24, 2025， https://www.atlassian.com/blog/artificial-intelligence/ai-agentic-workflows
What is an AI agent? - McKinsey, 访问时间为 六月 24, 2025， https://www.mckinsey.com/featured-insights/mckinsey-explainers/what-is-an-ai-agent
Building Multi AI Agent Workflows With LangChain In 2025 - Intuz, 访问时间为 六月 24, 2025， https://www.intuz.com/blog/building-multi-ai-agent-workflows-with-langchain
inference-examples/ai-workflow-langchain/README.md at main - GitHub, 访问时间为 六月 24, 2025， https://github.com/Cerebras/inference-examples/blob/main/ai-workflow-langchain/README.md
How to Build an AI Agent in Python: A Step-by-Step Tutorial for Advanced Developer, 访问时间为 六月 24, 2025， https://b-eye.com/blog/how-to-build-an-ai-agent-in-python/
TheSethRose/AI-File-Organizer-Agent - GitHub, 访问时间为 六月 24, 2025， https://github.com/TheSethRose/AI-File-Organizer-Agent
Orchestrating Multi-Agent AI Systems: When Should You Expand to Using Multiple Agents?, 访问时间为 六月 24, 2025， https://www.willowtreeapps.com/craft/multi-agent-ai-systems-when-to-expand
Novel Writer AI Agent For Creative Fiction Writing by Tars, 访问时间为 六月 24, 2025， https://hellotars.com/ai-agents/novel-writer-ai-agent
Character Creator AI Agent | ClickUp™, 访问时间为 六月 24, 2025， https://clickup.com/p/ai-agents/character-creator
AI Agents for Creative Writing: From Concepts to Full Drafts, 访问时间为 六月 24, 2025， https://www.okmg.com/blog/ai-agents-for-creative-writing-from-concepts-to-full-drafts
RaPID: Efficient Retrieval-Augmented Long Text Generation with Writing Planning and Information Discovery - arXiv, 访问时间为 六月 24, 2025， https://arxiv.org/html/2503.00751v1
raestrada/storycraftr: StoryCraftr is an open-source AI ... - GitHub, 访问时间为 六月 24, 2025， https://github.com/raestrada/storycraftr
The 2025 Guide to Retrieval-Augmented Generation (RAG) - Eden AI, 访问时间为 六月 24, 2025， https://www.edenai.co/post/the-2025-guide-to-retrieval-augmented-generation-rag
What is RAG? - Retrieval-Augmented Generation AI Explained - AWS, 访问时间为 六月 24, 2025， https://aws.amazon.com/what-is/retrieval-augmented-generation/
Multi-Agent Actor-Critic Generative AI for Query Resolution and Analysis - arXiv, 访问时间为 六月 24, 2025， https://arxiv.org/abs/2502.13164
Self-RAG: AI That Knows When to Double-Check - Analytics Vidhya, 访问时间为 六月 24, 2025， https://www.analyticsvidhya.com/blog/2025/01/self-rag/
[2402.08874] Recurrent Alignment with Hard Attention for Hierarchical Text Rating - arXiv, 访问时间为 六月 24, 2025， https://arxiv.org/abs/2402.08874
[D] HighNoon LLM: Exploring Hierarchical Memory for Efficient NLP - Reddit, 访问时间为 六月 24, 2025， https://www.reddit.com/r/MachineLearning/comments/1lcjjd2/d_highnoon_llm_exploring_hierarchical_memory_for/
HM-RAG: Hierarchical Multi-Agent Multimodal Retrieval Augmented Generation - arXiv, 访问时间为 六月 24, 2025， https://arxiv.org/html/2504.12330v1
Retrieval-Augmented Generation with Hierarchical Knowledge - arXiv, 访问时间为 六月 24, 2025， https://arxiv.org/html/2503.10150v1
[2502.09891] ArchRAG: Attributed Community-based Hierarchical Retrieval-Augmented Generation - arXiv, 访问时间为 六月 24, 2025， https://arxiv.org/abs/2502.09891
HM-RAG: Hierarchical Multi-Agent Multimodal Retrieval ... - arXiv, 访问时间为 六月 24, 2025， https://arxiv.org/abs/2504.12330
Top 13 Advanced RAG Techniques for Your Next Project, 访问时间为 六月 24, 2025， https://www.analyticsvidhya.com/blog/2025/04/advanced-rag-techniques/
Advanced RAG Techniques: What They Are & How to Use Them - FalkorDB, 访问时间为 六月 24, 2025， https://www.falkordb.com/blog/advanced-rag/
How to Build Agentic AI with LangChain and LangGraph - Codecademy, 访问时间为 六月 24, 2025， https://www.codecademy.com/article/agentic-ai-with-langchain-langgraph
Self RAG Explained: Teaching AI to Evaluate Its Own Responses - Machine Learning Plus, 访问时间为 六月 24, 2025， https://www.machinelearningplus.com/gen-ai/self-rag-explained-teaching-ai-to-evaluate-its-own-responses/
Build an Agent - ️ LangChain, 访问时间为 六月 24, 2025， https://python.langchain.com/docs/tutorials/agents/
Implementing ReAct Agentic Pattern From Scratch, 访问时间为 六月 24, 2025， https://www.dailydoseofds.com/ai-agents-crash-course-part-10-with-implementation/
What is a ReAct Agent? | IBM, 访问时间为 六月 24, 2025， https://www.ibm.com/think/topics/react-agent
ReAct - Prompt Engineering Guide, 访问时间为 六月 24, 2025， https://www.promptingguide.ai/techniques/react
Advanced RAG Techniques - Pinecone, 访问时间为 六月 24, 2025， https://www.pinecone.io/learn/advanced-rag-techniques/
Self-RAG: Learning to Retrieve, Generate and Critique through Self-Reflection, 访问时间为 六月 24, 2025， https://selfrag.github.io/
Self-RAG - Learn Prompting, 访问时间为 六月 24, 2025， https://learnprompting.org/docs/retrieval_augmented_generation/self-rag
“爽文”与“爽文化”背后的青年心理需求与引导策略 - 人民日报, 访问时间为 六月 24, 2025， http://paper.people.com.cn/rmlt/pc/content/202502/17/content_30060605.html
创作需“于心上用功”--艺术 - 中国作家网, 访问时间为 六月 24, 2025， http://www.chinawriter.com.cn/n1/2018/0904/c419388-30269805.html
脑洞大开的网文“金手指”，如何突破“无脑开挂” - 新华报业网, 访问时间为 六月 24, 2025， https://www.xhby.net/content/s67b8892ee4b05084aa3f5761.html
Interactive AI with Retrieval-Augmented Generation for Next Generation Networking - arXiv, 访问时间为 六月 24, 2025， https://arxiv.org/html/2401.11391v1
[2401.17268] Weaver: Foundation Models for Creative Writing - arXiv, 访问时间为 六月 24, 2025， https://arxiv.org/abs/2401.17268
杨正宇｜元宇宙视角下展览行为的立法新定位 - 解放日报, 访问时间为 六月 24, 2025， https://www.jfdaily.com/sgh/detail?id=1599336
文旅微短剧是否需要“爽感”叙事 - 光明网, 访问时间为 六月 24, 2025， https://news.gmw.cn/2025-03/01/content_37880189.htm
女主六集还没登场？央视大剧，底气十足, 访问时间为 六月 24, 2025， https://news.bjd.com.cn/2025/06/23/11208764.shtml
网络文学中的主角中心主义与群像式书写 - 中国作家网, 访问时间为 六月 24, 2025， https://www.chinawriter.com.cn/n1/2022/0214/c404027-32351434.html
