#!/bin/bash

# =============================================================================
# AI智能体后端快速启动脚本
# 适用于已经部署但服务停止的情况
# =============================================================================

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

PROJECT_PATH="/www/wwwroot/ai_agent_backend"
SERVICE_NAME="ai-agent"

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 检查项目是否存在
check_project() {
    if [ ! -d "$PROJECT_PATH" ]; then
        print_error "项目目录不存在: $PROJECT_PATH"
        print_info "请先运行部署脚本: bash bt_deploy.sh"
        exit 1
    fi
    print_success "项目目录存在"
}

# 检查服务状态
check_service() {
    if systemctl is-active --quiet $SERVICE_NAME; then
        print_warning "服务已经在运行中"
        return 0
    else
        print_info "服务未运行，准备启动"
        return 1
    fi
}

# 启动服务
start_service() {
    print_info "启动AI智能体服务..."
    
    systemctl start $SERVICE_NAME
    sleep 3
    
    if systemctl is-active --quiet $SERVICE_NAME; then
        print_success "服务启动成功"
    else
        print_error "服务启动失败"
        systemctl status $SERVICE_NAME
        exit 1
    fi
}

# 测试服务
test_service() {
    print_info "测试服务连接..."
    
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s http://localhost:8000/health > /dev/null; then
            print_success "服务连接正常"
            return 0
        fi
        
        print_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    print_error "服务连接失败"
    return 1
}

# 显示状态
show_status() {
    local ip=$(curl -s ifconfig.me 2>/dev/null || echo "YOUR_SERVER_IP")
    
    echo
    print_success "🎉 AI智能体后端服务已启动！"
    echo
    echo "📊 服务状态: $(systemctl is-active $SERVICE_NAME)"
    echo "🌐 访问地址: http://$ip:8000"
    echo "📚 API文档: http://$ip:8000/docs"
    echo "❤️  健康检查: http://$ip:8000/health"
    echo "🔗 API地址: http://$ip:8000/api/v1/ai-agents"
    echo
    echo "🔧 管理命令:"
    echo "  systemctl status $SERVICE_NAME     # 查看状态"
    echo "  systemctl restart $SERVICE_NAME    # 重启服务"
    echo "  systemctl stop $SERVICE_NAME       # 停止服务"
    echo "  journalctl -u $SERVICE_NAME -f     # 查看日志"
    echo
}

# 主函数
main() {
    echo "🚀 启动AI智能体后端服务..."
    
    check_project
    
    if check_service; then
        if test_service; then
            show_status
        else
            print_warning "服务运行但连接失败，尝试重启..."
            systemctl restart $SERVICE_NAME
            sleep 5
            if test_service; then
                show_status
            else
                print_error "重启后仍无法连接，请检查日志"
                journalctl -u $SERVICE_NAME --no-pager -n 20
            fi
        fi
    else
        start_service
        if test_service; then
            show_status
        else
            print_error "启动失败，请检查日志"
            journalctl -u $SERVICE_NAME --no-pager -n 20
        fi
    fi
}

# 运行主函数
main "$@"
