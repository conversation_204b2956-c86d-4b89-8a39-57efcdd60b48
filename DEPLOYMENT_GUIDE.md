# 🚀 AI智能体后端一键部署指南

## 📋 部署前准备

### 1. 服务器要求
- **操作系统**: Ubuntu 18.04+ / CentOS 7+ / Debian 9+
- **内存**: 最少2GB，推荐4GB+
- **存储**: 最少10GB可用空间
- **网络**: 稳定的互联网连接

### 2. 宝塔面板安装
如果还没有安装宝塔面板，请先安装：

```bash
# Ubuntu/Debian
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh

# CentOS
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh
```

## 🎯 一键部署

### 方法一：使用简化部署脚本（推荐）

1. **下载部署脚本**
```bash
wget https://raw.githubusercontent.com/your-repo/ai-agent-backend/main/bt_deploy.sh
# 或者直接创建文件并复制内容
```

2. **运行部署脚本**
```bash
chmod +x bt_deploy.sh
sudo bash bt_deploy.sh
```

3. **等待部署完成**
脚本会自动完成以下步骤：
- ✅ 检查宝塔面板
- ✅ 创建项目目录
- ✅ 设置Python环境
- ✅ 安装依赖包
- ✅ 创建应用代码
- ✅ 配置系统服务
- ✅ 启动服务
- ✅ 测试部署

### 方法二：使用完整部署脚本

如果需要更多功能（如Nginx配置、SSL证书等）：

```bash
wget https://raw.githubusercontent.com/your-repo/ai-agent-backend/main/deploy_ai_agent.sh
chmod +x deploy_ai_agent.sh
sudo bash deploy_ai_agent.sh
```

## 📱 Flutter应用配置

部署完成后，需要在Flutter应用中更新API地址：

### 1. 更新服务配置
在 `lib/services/ai_agent_novel_service.dart` 中：

```dart
class AIAgentNovelService {
  // 替换为您的服务器IP地址
  static const String _baseUrl = 'http://YOUR_SERVER_IP:8000/api/v1/ai-agents';
  
  // 如果配置了域名，可以使用：
  // static const String _baseUrl = 'https://yourdomain.com/api/v1/ai-agents';
}
```

### 2. 测试连接
1. 在Flutter应用中进入AI智能体界面
2. 输入一些测试提示
3. 点击"开始创作"按钮
4. 查看是否能正常连接到后端服务

## 🔧 服务管理

### 查看服务状态
```bash
systemctl status ai-agent
```

### 重启服务
```bash
systemctl restart ai-agent
```

### 查看日志
```bash
journalctl -u ai-agent -f
```

### 停止服务
```bash
systemctl stop ai-agent
```

## 🌐 访问地址

部署完成后，您可以通过以下地址访问：

- **主页**: `http://YOUR_SERVER_IP:8000/`
- **API文档**: `http://YOUR_SERVER_IP:8000/docs`
- **健康检查**: `http://YOUR_SERVER_IP:8000/health`
- **API基础URL**: `http://YOUR_SERVER_IP:8000/api/v1/ai-agents`

## 🔒 安全配置（可选）

### 1. 配置防火墙
```bash
# 开放必要端口
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 8000/tcp
ufw enable
```

### 2. 配置域名和SSL
如果有域名，建议配置SSL证书：

1. 在宝塔面板中添加站点
2. 配置反向代理到 `http://127.0.0.1:8000`
3. 申请SSL证书
4. 强制HTTPS

### 3. 限制访问
在 `.env` 文件中修改CORS配置：
```bash
# 限制特定域名访问
ALLOWED_ORIGINS="https://yourdomain.com,https://www.yourdomain.com"
```

## 🛠️ 故障排除

### 1. 服务无法启动
```bash
# 查看详细错误信息
systemctl status ai-agent
journalctl -u ai-agent --no-pager

# 检查端口是否被占用
netstat -tlnp | grep 8000

# 手动启动测试
cd /www/wwwroot/ai_agent_backend
source venv/bin/activate
python run.py
```

### 2. 权限问题
```bash
# 重新设置权限
chown -R www:www /www/wwwroot/ai_agent_backend
chmod -R 755 /www/wwwroot/ai_agent_backend
```

### 3. 依赖安装失败
```bash
# 更新pip并重新安装
cd /www/wwwroot/ai_agent_backend
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
```

### 4. 网络连接问题
```bash
# 检查防火墙状态
ufw status

# 检查端口监听
ss -tlnp | grep 8000

# 测试本地连接
curl http://localhost:8000/health
```

## 📊 性能优化

### 1. 增加工作进程
编辑 `run.py`，增加workers数量：
```python
uvicorn.run(
    "app.main:app",
    host="0.0.0.0",
    port=8000,
    workers=4  # 根据CPU核心数调整
)
```

### 2. 配置Nginx缓存
在Nginx配置中添加缓存设置：
```nginx
location /static/ {
    expires 30d;
    add_header Cache-Control "public, immutable";
}
```

### 3. 监控资源使用
```bash
# 查看内存使用
free -h

# 查看CPU使用
top

# 查看磁盘使用
df -h
```

## 🔄 更新部署

### 1. 备份当前版本
```bash
cp -r /www/wwwroot/ai_agent_backend /www/wwwroot/ai_agent_backend_backup_$(date +%Y%m%d)
```

### 2. 更新代码
```bash
cd /www/wwwroot/ai_agent_backend
# 更新代码文件
# 重新安装依赖（如果需要）
source venv/bin/activate
pip install -r requirements.txt
```

### 3. 重启服务
```bash
systemctl restart ai-agent
```

## 📞 技术支持

如果遇到问题，请：

1. 查看服务日志：`journalctl -u ai-agent -f`
2. 检查网络连接：`curl http://localhost:8000/health`
3. 验证配置文件：检查 `.env` 文件内容
4. 查看系统资源：`htop` 或 `top`

## 🎉 部署成功

恭喜！您已经成功部署了AI智能体后端服务。现在可以：

1. ✅ 在Flutter应用中配置API地址
2. ✅ 测试小说生成功能
3. ✅ 享受AI智能体协作创作的乐趣

---

**注意**: 这是一个基础版本的后端服务，主要用于演示和测试。生产环境建议添加数据库、认证、日志记录等功能。
