"""
编排者智能体 - 系统的项目经理，负责协调整个创作流程
"""
from typing import Dict, Any, List
from langchain.prompts import PromptTemplate
from .base_agent import BaseAgent
from .tools import get_common_tools
import logging

logger = logging.getLogger(__name__)

class OrchestratorAgent(BaseAgent):
    """编排者智能体 - 负责整个工作流程的协调和管理"""
    
    def __init__(self, llm=None):
        super().__init__(
            name="编排者",
            description="系统的项目经理，负责协调整个小说创作流程",
            llm=llm,
            tools=get_common_tools(llm) if llm else []
        )
        self.workflow_state = {
            "current_phase": "initialization",
            "current_chapter": 0,
            "total_chapters": 0,
            "completed_tasks": [],
            "pending_tasks": [],
            "errors": []
        }
    
    def _get_system_prompt(self) -> str:
        return """
你是编排者智能体，负责协调整个小说创作系统的工作流程。你的职责包括：

1. 接收用户的初始创作需求
2. 制定详细的创作计划
3. 协调各个专业智能体的工作
4. 监控创作进度和质量
5. 处理异常情况和错误
6. 确保最终输出符合要求

工作流程：
1. 初始化阶段：调用架构师创建世界观和大纲
2. 创作阶段：循环调用执笔者和校订者
3. 完善阶段：根据需要进行修订和优化
4. 完成阶段：整理最终输出

你需要根据当前状态决定下一步调用哪个智能体，并处理他们的输出结果。
"""
    
    def _get_prompt_template(self) -> PromptTemplate:
        return PromptTemplate(
            input_variables=["input", "chat_history", "agent_scratchpad"],
            template="""
{system_prompt}

当前工作流程状态：
- 当前阶段：{current_phase}
- 当前章节：{current_chapter}/{total_chapters}
- 已完成任务：{completed_tasks}
- 待处理任务：{pending_tasks}

用户输入或任务：{input}

聊天历史：{chat_history}

你有以下工具可以使用：
{tools}

使用以下格式进行推理和行动：

思考：我需要分析当前情况并决定下一步行动
行动：[选择一个工具]
行动输入：[工具的输入参数]
观察：[工具的输出结果]
... (可以重复思考/行动/观察)
思考：我现在知道最终答案了
最终答案：[你的最终回答]

开始！

{agent_scratchpad}
""".format(
                system_prompt=self._get_system_prompt(),
                current_phase=self.workflow_state["current_phase"],
                current_chapter=self.workflow_state["current_chapter"],
                total_chapters=self.workflow_state["total_chapters"],
                completed_tasks=self.workflow_state["completed_tasks"],
                pending_tasks=self.workflow_state["pending_tasks"],
                tools="{tools}",
                input="{input}",
                chat_history="{chat_history}",
                agent_scratchpad="{agent_scratchpad}"
            )
        )
    
    def _direct_action(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """直接行动方法"""
        task_type = plan.get("task_type", "unknown")
        
        if task_type == "initialize_project":
            return self._initialize_project(plan)
        elif task_type == "plan_workflow":
            return self._plan_workflow(plan)
        elif task_type == "coordinate_agents":
            return self._coordinate_agents(plan)
        elif task_type == "monitor_progress":
            return self._monitor_progress(plan)
        else:
            return {"success": False, "error": f"未知任务类型: {task_type}"}
    
    def _initialize_project(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """初始化项目"""
        try:
            user_prompt = plan.get("user_prompt", "")
            target_chapters = plan.get("target_chapters", 100)
            
            # 更新工作流程状态
            self.workflow_state.update({
                "current_phase": "initialization",
                "total_chapters": target_chapters,
                "pending_tasks": [
                    "create_world_bible",
                    "create_character_profiles", 
                    "create_plot_outline",
                    "begin_chapter_generation"
                ]
            })
            
            logger.info(f"项目初始化完成，目标章节数：{target_chapters}")
            
            return {
                "success": True,
                "message": "项目初始化完成",
                "next_action": "call_architect",
                "workflow_state": self.workflow_state
            }
            
        except Exception as e:
            logger.error(f"项目初始化失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _plan_workflow(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """制定工作流程计划"""
        try:
            phase = self.workflow_state["current_phase"]
            
            if phase == "initialization":
                next_steps = [
                    {"agent": "architect", "task": "create_world_bible"},
                    {"agent": "architect", "task": "create_character_profiles"},
                    {"agent": "architect", "task": "create_plot_outline"}
                ]
            elif phase == "generation":
                next_steps = [
                    {"agent": "chronicler", "task": "write_chapter"},
                    {"agent": "editor", "task": "review_chapter"},
                    {"agent": "lorekeeper", "task": "update_memory"}
                ]
            else:
                next_steps = []
            
            return {
                "success": True,
                "next_steps": next_steps,
                "current_phase": phase
            }
            
        except Exception as e:
            logger.error(f"工作流程规划失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _coordinate_agents(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """协调其他智能体"""
        try:
            target_agent = plan.get("target_agent")
            task = plan.get("task")
            parameters = plan.get("parameters", {})
            
            # 这里应该调用相应的智能体
            # 在实际实现中，这里会调用其他智能体的execute方法
            
            logger.info(f"协调智能体 {target_agent} 执行任务 {task}")
            
            return {
                "success": True,
                "message": f"已协调 {target_agent} 执行 {task}",
                "agent": target_agent,
                "task": task
            }
            
        except Exception as e:
            logger.error(f"智能体协调失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _monitor_progress(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """监控创作进度"""
        try:
            progress = {
                "current_chapter": self.workflow_state["current_chapter"],
                "total_chapters": self.workflow_state["total_chapters"],
                "completion_rate": (
                    self.workflow_state["current_chapter"] / 
                    max(self.workflow_state["total_chapters"], 1) * 100
                ),
                "current_phase": self.workflow_state["current_phase"],
                "completed_tasks": len(self.workflow_state["completed_tasks"]),
                "pending_tasks": len(self.workflow_state["pending_tasks"]),
                "errors": len(self.workflow_state["errors"])
            }
            
            return {
                "success": True,
                "progress": progress,
                "workflow_state": self.workflow_state
            }
            
        except Exception as e:
            logger.error(f"进度监控失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def update_workflow_state(self, updates: Dict[str, Any]):
        """更新工作流程状态"""
        self.workflow_state.update(updates)
        logger.info(f"工作流程状态已更新: {updates}")
    
    def get_workflow_state(self) -> Dict[str, Any]:
        """获取当前工作流程状态"""
        return self.workflow_state.copy()
