# 🚀 AI智能体后端一键部署包

## 📦 部署包内容

```
ai-agent-deployment/
├── bt_deploy.sh              # 宝塔面板一键部署脚本（推荐）
├── deploy_ai_agent.sh        # 完整部署脚本（包含Nginx等）
├── quick_start.sh            # 快速启动脚本
├── DEPLOYMENT_GUIDE.md       # 详细部署指南
└── README_DEPLOYMENT.md      # 本文件
```

## ⚡ 快速开始

### 1. 最简单的方式（推荐）

```bash
# 1. 下载部署脚本
wget https://raw.githubusercontent.com/your-repo/ai-agent-backend/main/bt_deploy.sh

# 2. 运行一键部署
chmod +x bt_deploy.sh
sudo bash bt_deploy.sh

# 3. 等待部署完成（约3-5分钟）
```

### 2. 如果服务停止了

```bash
# 快速重启服务
bash quick_start.sh
```

## 🎯 部署后配置

### Flutter应用配置

部署完成后，在您的Flutter应用中更新API地址：

```dart
// lib/services/ai_agent_novel_service.dart
class AIAgentNovelService {
  static const String _baseUrl = 'http://YOUR_SERVER_IP:8000/api/v1/ai-agents';
  // 替换 YOUR_SERVER_IP 为您的实际服务器IP
}
```

### 测试连接

1. 打开浏览器访问：`http://YOUR_SERVER_IP:8000/docs`
2. 在Flutter应用中测试AI智能体功能

## 📋 系统要求

- **服务器**: Ubuntu 18.04+ / CentOS 7+ / Debian 9+
- **内存**: 最少2GB，推荐4GB+
- **存储**: 最少10GB可用空间
- **宝塔面板**: 已安装（脚本会检查）

## 🔧 部署脚本说明

### bt_deploy.sh（推荐）
- ✅ 轻量级，专为宝塔面板优化
- ✅ 自动创建Python环境
- ✅ 自动安装依赖
- ✅ 配置系统服务
- ✅ 包含基础的AI智能体API

### deploy_ai_agent.sh（完整版）
- ✅ 包含所有bt_deploy.sh功能
- ✅ 自动配置Nginx反向代理
- ✅ 自动配置防火墙
- ✅ 包含SSL证书配置
- ✅ 更完整的错误处理

### quick_start.sh（维护工具）
- ✅ 快速启动已部署的服务
- ✅ 检查服务状态
- ✅ 自动重启故障服务
- ✅ 显示访问信息

## 🌐 访问地址

部署成功后，您可以访问：

| 功能 | 地址 | 说明 |
|------|------|------|
| 主页 | `http://YOUR_IP:8000/` | 服务状态页面 |
| API文档 | `http://YOUR_IP:8000/docs` | 交互式API文档 |
| 健康检查 | `http://YOUR_IP:8000/health` | 服务健康状态 |
| API基础URL | `http://YOUR_IP:8000/api/v1/ai-agents` | Flutter应用配置地址 |

## 🔒 安全建议

### 1. 防火墙配置
```bash
# 只开放必要端口
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw allow 8000/tcp  # AI Agent API
ufw enable
```

### 2. 域名和SSL（推荐）
如果有域名，建议：
1. 在宝塔面板中添加站点
2. 配置反向代理到 `http://127.0.0.1:8000`
3. 申请免费SSL证书
4. 强制HTTPS访问

### 3. 访问限制
编辑 `/www/wwwroot/ai_agent_backend/.env`：
```bash
# 限制特定域名访问
ALLOWED_ORIGINS="https://yourdomain.com"
```

## 🛠️ 常用管理命令

```bash
# 查看服务状态
systemctl status ai-agent

# 重启服务
systemctl restart ai-agent

# 停止服务
systemctl stop ai-agent

# 查看实时日志
journalctl -u ai-agent -f

# 查看最近日志
journalctl -u ai-agent --no-pager -n 50

# 检查端口占用
netstat -tlnp | grep 8000

# 测试API连接
curl http://localhost:8000/health
```

## 🔄 更新和维护

### 更新代码
```bash
# 1. 备份当前版本
cp -r /www/wwwroot/ai_agent_backend /www/wwwroot/ai_agent_backend_backup

# 2. 更新代码文件
cd /www/wwwroot/ai_agent_backend
# 替换新的代码文件

# 3. 重启服务
systemctl restart ai-agent
```

### 定期维护
```bash
# 清理日志（保留最近30天）
journalctl --vacuum-time=30d

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

## 🆘 故障排除

### 1. 服务无法启动
```bash
# 查看详细错误
systemctl status ai-agent
journalctl -u ai-agent --no-pager

# 检查端口冲突
netstat -tlnp | grep 8000

# 手动测试启动
cd /www/wwwroot/ai_agent_backend
source venv/bin/activate
python run.py
```

### 2. 无法访问API
```bash
# 检查防火墙
ufw status

# 检查服务监听
ss -tlnp | grep 8000

# 测试本地连接
curl -v http://localhost:8000/health
```

### 3. 权限问题
```bash
# 重新设置权限
chown -R www:www /www/wwwroot/ai_agent_backend
chmod -R 755 /www/wwwroot/ai_agent_backend
```

## 📞 获取帮助

如果遇到问题：

1. **查看日志**: `journalctl -u ai-agent -f`
2. **检查配置**: 确认 `.env` 文件内容
3. **测试连接**: `curl http://localhost:8000/health`
4. **重启服务**: `systemctl restart ai-agent`

## 🎉 部署成功标志

当您看到以下内容时，说明部署成功：

```bash
✅ 部署完成！

📍 项目路径: /www/wwwroot/ai_agent_backend
🌐 访问地址: http://YOUR_IP:8000
📚 API文档: http://YOUR_IP:8000/docs
❤️  健康检查: http://YOUR_IP:8000/health
🔗 API地址: http://YOUR_IP:8000/api/v1/ai-agents

📱 Flutter应用配置:
  将API地址设置为: http://YOUR_IP:8000/api/v1/ai-agents
```

现在您可以在Flutter应用中配置API地址，开始使用AI智能体功能了！

---

**提示**: 这是一个演示版本的后端服务，包含模拟的AI生成功能。在生产环境中，您需要配置真实的AI模型API密钥。
