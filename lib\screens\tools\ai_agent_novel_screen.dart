import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/ai_agent_novel_controller.dart';
import 'package:novel_app/screens/settings_screen.dart';

class AIAgentNovelScreen extends StatelessWidget {
  const AIAgentNovelScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AIAgentNovelController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('AI智能体小说生成'),
        backgroundColor: Colors.purple.shade100,
      ),
      body: Obx(() {
        if (controller.isGenerating.value) {
          return _buildGeneratingView(controller);
        } else if (controller.generationResult.value != null) {
          return _buildResultView(controller);
        } else {
          return _buildInputView(controller);
        }
      }),
    );
  }

  Widget _buildConfigItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputView(AIAgentNovelController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 系统介绍卡片
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.auto_awesome, color: Colors.purple.shade600),
                      const SizedBox(width: 8),
                      Text(
                        'AI智能体协作创作系统',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.purple.shade600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    '基于多智能体架构的自主小说创作系统，包含编排者、架构师、执笔者、典籍官、校订者五大智能体，'
                    '能够自主生成百万字级"爽文"小说，具备完整的世界观构建、角色设计、情节规划和质量检查能力。',
                    style: TextStyle(fontSize: 14, height: 1.4),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    children: [
                      Chip(
                        label: const Text('多智能体协作'),
                        backgroundColor: Colors.purple.shade50,
                      ),
                      Chip(
                        label: const Text('分层RAG记忆'),
                        backgroundColor: Colors.blue.shade50,
                      ),
                      Chip(
                        label: const Text('爽文范式'),
                        backgroundColor: Colors.orange.shade50,
                      ),
                      Chip(
                        label: const Text('自我批判'),
                        backgroundColor: Colors.green.shade50,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // 创作提示输入
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '创作提示',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: controller.promptController,
                    maxLines: 4,
                    decoration: const InputDecoration(
                      hintText: '请描述您想要创作的小说类型、主角设定、金手指等...\n\n例如：一部修仙小说，主角的金手指是一个可以调试物理法则的系统',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 12),
                  // 快速模板
                  const Text('快速模板：'),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      _buildTemplateChip(
                        controller,
                        '修仙系统',
                        '一部修仙小说，主角的金手指是一个可以调试物理法则的系统',
                      ),
                      _buildTemplateChip(
                        controller,
                        '都市异能',
                        '一部都市异能小说，主角获得了时间回溯的能力',
                      ),
                      _buildTemplateChip(
                        controller,
                        '玄幻吞噬',
                        '一部玄幻小说，主角拥有吞噬万物进化的天赋',
                      ),
                      _buildTemplateChip(
                        controller,
                        '末世空间',
                        '一部科幻小说，主角在末世中觉醒了空间异能',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 生成设置
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '生成设置',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      const Text('目标章节数：'),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Obx(() => Slider(
                              value: controller.targetChapters.value.toDouble(),
                              min: 5,
                              max: 100,
                              divisions: 19,
                              label: '${controller.targetChapters.value}章',
                              onChanged: (value) {
                                controller.targetChapters.value = value.toInt();
                              },
                            )),
                      ),
                      Obx(() => Text('${controller.targetChapters.value}章')),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      const Text('小说类型：'),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Obx(() => DropdownButton<String>(
                              value: controller.selectedGenre.value,
                              isExpanded: true,
                              items: controller.genres.map((genre) {
                                return DropdownMenuItem(
                                  value: genre,
                                  child: Text(genre),
                                );
                              }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  controller.selectedGenre.value = value;
                                }
                              },
                            )),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 模型配置
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Text(
                        '模型配置',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const Spacer(),
                      TextButton.icon(
                        onPressed: () => Get.to(() => const SettingsScreen()),
                        icon: const Icon(Icons.settings),
                        label: const Text('配置模型'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // 显示当前配置
                  Obx(() {
                    final config = controller.currentModelConfig;
                    return Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.smart_toy, color: Colors.blue.shade600),
                              const SizedBox(width: 8),
                              Text(
                                '当前模型配置',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          _buildConfigItem('模型', config.model),
                          _buildConfigItem('API地址', config.apiUrl),
                          _buildConfigItem('温度', config.temperature.toString()),
                          _buildConfigItem('最大Token', config.maxTokens.toString()),
                          if (config.apiKey.isNotEmpty)
                            _buildConfigItem('API密钥', '已配置 (${config.apiKey.substring(0, 8)}...)'),
                        ],
                      ),
                    );
                  }),
                  const SizedBox(height: 12),

                  // 配置提示
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.orange.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.orange.shade600),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            '点击"配置模型"按钮可以修改API设置，支持OpenAI、本地模型等多种选择',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.orange.shade800,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // 开始生成按钮
          SizedBox(
            width: double.infinity,
            height: 50,
            child: Obx(() => ElevatedButton.icon(
                  onPressed: controller.promptText.value.trim().isEmpty
                      ? null
                      : () => controller.startGeneration(),
                  icon: const Icon(Icons.auto_awesome),
                  label: const Text('开始AI智能体创作'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple.shade600,
                    foregroundColor: Colors.white,
                    textStyle: const TextStyle(fontSize: 16),
                  ),
                )),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateChip(
      AIAgentNovelController controller, String label, String template) {
    return ActionChip(
      label: Text(label),
      onPressed: () {
        controller.promptController.text = template;
      },
    );
  }

  Widget _buildGeneratingView(AIAgentNovelController controller) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 进度卡片
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  const Text(
                    'AI智能体正在协作创作中...',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Obx(() => Text(
                        controller.currentStatus.value,
                        style: const TextStyle(fontSize: 14),
                        textAlign: TextAlign.center,
                      )),
                  const SizedBox(height: 16),
                  Obx(() => LinearProgressIndicator(
                        value: controller.progress.value,
                        backgroundColor: Colors.grey.shade300,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.purple.shade600,
                        ),
                      )),
                  const SizedBox(height: 8),
                  Obx(() => Text(
                        '${(controller.progress.value * 100).toInt()}%',
                        style: const TextStyle(fontSize: 12),
                      )),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),

          // 智能体状态
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '智能体工作状态',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),
                  Obx(() => Column(
                        children: controller.agentStatuses.entries.map((entry) {
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4),
                            child: Row(
                              children: [
                                Icon(
                                  entry.value == 'active'
                                      ? Icons.play_circle_filled
                                      : entry.value == 'completed'
                                          ? Icons.check_circle
                                          : Icons.radio_button_unchecked,
                                  color: entry.value == 'active'
                                      ? Colors.blue
                                      : entry.value == 'completed'
                                          ? Colors.green
                                          : Colors.grey,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(entry.key),
                                const Spacer(),
                                Text(
                                  entry.value == 'active'
                                      ? '工作中'
                                      : entry.value == 'completed'
                                          ? '已完成'
                                          : '等待中',
                                  style: TextStyle(
                                    color: entry.value == 'active'
                                        ? Colors.blue
                                        : entry.value == 'completed'
                                            ? Colors.green
                                            : Colors.grey,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                      )),
                ],
              ),
            ),
          ),
          const Spacer(),

          // 取消按钮
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () => controller.cancelGeneration(),
              child: const Text('取消生成'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultView(AIAgentNovelController controller) {
    final result = controller.generationResult.value;
    if (result == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 结果概览
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        result['success'] ? Icons.check_circle : Icons.error,
                        color: result['success'] ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        result['success'] ? '生成成功！' : '生成失败',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: result['success'] ? Colors.green : Colors.red,
                        ),
                      ),
                    ],
                  ),
                  if (result['success']) ...[
                    const SizedBox(height: 12),
                    Text('完成章节：${result['completed_chapters']}/${result['target_chapters']}'),
                    Text('总字数：${result['total_words'] ?? '未知'}'),
                    Text('生成时间：${result['generated_at'] ?? '未知'}'),
                  ] else ...[
                    const SizedBox(height: 12),
                    Text('错误信息：${result['error'] ?? '未知错误'}'),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 操作按钮
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => controller.resetGeneration(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('重新生成'),
                ),
              ),
              const SizedBox(width: 12),
              if (result['success'])
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => controller.downloadNovel(),
                    icon: const Icon(Icons.download),
                    label: const Text('下载小说'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),

          // 详细信息
          if (result['success']) ...[
            _buildDetailCard('世界观设定', result['world_bible']),
            _buildDetailCard('角色档案', result['characters']),
            _buildDetailCard('情节大纲', result['outline']),
            if (result['chapters'] != null && result['chapters'].isNotEmpty)
              _buildChaptersCard(result['chapters']),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailCard(String title, dynamic data) {
    if (data == null) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        title: Text(title),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              data.toString(),
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChaptersCard(List chapters) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ExpansionTile(
        title: Text('章节内容 (${chapters.length}章)'),
        children: chapters.map<Widget>((chapter) {
          return ListTile(
            title: Text('第${chapter['chapter']}章'),
            subtitle: Text(
              '${chapter['word_count']}字',
              style: const TextStyle(fontSize: 12),
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              // 显示章节详情
              Get.dialog(
                AlertDialog(
                  title: Text('第${chapter['chapter']}章'),
                  content: SingleChildScrollView(
                    child: Text(chapter['content'] ?? ''),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Get.back(),
                      child: const Text('关闭'),
                    ),
                  ],
                ),
              );
            },
          );
        }).toList(),
      ),
    );
  }
}
