"""
架构师智能体 - 负责世界构建与大纲规划
"""
from typing import Dict, Any, List
from langchain.prompts import PromptTemplate
from .base_agent import BaseAgent
from .tools import get_common_tools
import json
import logging

logger = logging.getLogger(__name__)

class ArchitectAgent(BaseAgent):
    """架构师智能体 - 系统的创意总监，负责世界观设定和情节大纲"""
    
    def __init__(self, llm=None):
        super().__init__(
            name="架构师",
            description="系统的创意总监，负责世界观设定、角色创建和情节大纲规划",
            llm=llm,
            tools=get_common_tools(llm) if llm else []
        )
    
    def _get_system_prompt(self) -> str:
        return """
你是架构师智能体，负责小说的整体架构设计。你的职责包括：

1. 世界观构建：创建详细的世界设定文档(world_bible.json)
2. 角色设计：设计主要角色及其成长弧线(characters.json)
3. 情节规划：制定从主线到章节的分层大纲(outline.json)
4. "爽文"范式实现：确保符合爽文创作规律

特别注意"爽文"创作要求：
- 黄金三章：前三章必须建立主角困境、引入冲突、展示"金手指"
- 紧凑节奏：每5-10章安排一个"爽点"（复仇、升级、打脸等）
- 主角光环：明确定义"金手指"的规则、限制和代价
- 逆袭弧线：主角能力/地位必须呈上升趋势
- 脸谱化反派：简单直接的对立角色，便于打脸

你需要将这些抽象概念转化为具体的、结构化的创作蓝图。
"""
    
    def _get_prompt_template(self) -> PromptTemplate:
        return PromptTemplate(
            input_variables=["input", "chat_history", "agent_scratchpad"],
            template="""
{system_prompt}

你有以下工具可以使用：
{tools}

用户需求：{input}

聊天历史：{chat_history}

使用以下格式进行推理和行动：

思考：我需要分析用户需求并制定创作架构
行动：[选择一个工具]
行动输入：[工具的输入参数]
观察：[工具的输出结果]
... (可以重复思考/行动/观察)
思考：我现在知道最终答案了
最终答案：[你的最终回答]

开始！

{agent_scratchpad}
""".format(
                system_prompt=self._get_system_prompt(),
                tools="{tools}",
                input="{input}",
                chat_history="{chat_history}",
                agent_scratchpad="{agent_scratchpad}"
            )
        )
    
    def _direct_action(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """直接行动方法"""
        task_type = plan.get("task_type", "unknown")
        
        if task_type == "create_world_bible":
            return self._create_world_bible(plan)
        elif task_type == "create_character_profiles":
            return self._create_character_profiles(plan)
        elif task_type == "create_plot_outline":
            return self._create_plot_outline(plan)
        elif task_type == "expand_worldbuilding":
            return self._expand_worldbuilding(plan)
        else:
            return {"success": False, "error": f"未知任务类型: {task_type}"}
    
    def _create_world_bible(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """创建世界观设定文档"""
        try:
            user_prompt = plan.get("user_prompt", "")
            genre = plan.get("genre", "修仙")
            
            # 生成世界观设定
            world_bible_prompt = f"""
基于用户需求创建详细的世界观设定：{user_prompt}

请创建一个完整的世界观设定，包含：

1. 世界基本设定
   - 世界名称和背景
   - 时代背景
   - 地理环境
   - 社会结构

2. 力量体系
   - 修炼/能力体系
   - 等级划分
   - 突破条件
   - 限制和代价

3. "金手指"设定
   - 名称和类型
   - 具体功能
   - 使用规则
   - 限制条件
   - 成长潜力

4. 重要势力
   - 主要门派/组织
   - 势力关系
   - 地盘分布

5. 重要地点
   - 关键场景
   - 地点特色
   - 相关传说

请以JSON格式输出，确保结构清晰、内容详细。
"""
            
            world_bible_content = self.llm.invoke(world_bible_prompt).content
            
            # 尝试解析JSON，如果失败则包装成JSON
            try:
                world_bible_data = json.loads(world_bible_content)
            except:
                world_bible_data = {
                    "world_name": "未命名世界",
                    "genre": genre,
                    "description": world_bible_content,
                    "created_by": "架构师智能体"
                }
            
            # 保存世界观文档
            filename = "data/world_bible.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(world_bible_data, f, ensure_ascii=False, indent=2)
            
            logger.info("世界观设定文档创建完成")
            
            return {
                "success": True,
                "message": "世界观设定文档创建完成",
                "filename": filename,
                "data": world_bible_data
            }
            
        except Exception as e:
            logger.error(f"世界观创建失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _create_character_profiles(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """创建角色档案"""
        try:
            user_prompt = plan.get("user_prompt", "")
            
            character_prompt = f"""
基于用户需求和世界观设定创建主要角色档案：{user_prompt}

请创建详细的角色设定，包含：

1. 主角设定
   - 姓名、年龄、外貌
   - 性格特点
   - 背景故事
   - 初始能力
   - 成长目标
   - "金手指"关系

2. 主要配角（3-5个）
   - 基本信息
   - 与主角关系
   - 角色作用
   - 成长弧线

3. 主要反派（3-5个）
   - 基本信息
   - 反派类型（傲慢少爷、嫉妒同辈等）
   - 与主角冲突点
   - 被打脸方式

4. 重要NPC
   - 师父/导师
   - 家族成员
   - 势力领袖

每个角色都要有清晰的动机和成长轨迹，特别是反派要符合"爽文"打脸套路。

请以JSON格式输出。
"""
            
            characters_content = self.llm.invoke(character_prompt).content
            
            # 尝试解析JSON
            try:
                characters_data = json.loads(characters_content)
            except:
                characters_data = {
                    "protagonist": {"name": "主角", "description": characters_content},
                    "supporting_characters": [],
                    "antagonists": [],
                    "npcs": [],
                    "created_by": "架构师智能体"
                }
            
            # 保存角色档案
            filename = "data/characters.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(characters_data, f, ensure_ascii=False, indent=2)
            
            logger.info("角色档案创建完成")
            
            return {
                "success": True,
                "message": "角色档案创建完成",
                "filename": filename,
                "data": characters_data
            }
            
        except Exception as e:
            logger.error(f"角色创建失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _create_plot_outline(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """创建情节大纲"""
        try:
            target_chapters = plan.get("target_chapters", 100)
            user_prompt = plan.get("user_prompt", "")
            
            outline_prompt = f"""
基于用户需求、世界观和角色设定创建详细的情节大纲：{user_prompt}
目标章节数：{target_chapters}

请创建分层的情节大纲，包含：

1. 总体情节弧线
   - 起始状态：主角的困境
   - 发展阶段：能力提升和冲突升级
   - 高潮阶段：最终对决
   - 结局状态：逆袭成功

2. 分卷规划（每卷20-30章）
   - 卷名和主题
   - 主要冲突
   - 关键"爽点"
   - 能力提升节点

3. 黄金三章详细规划
   - 第一章：主角困境展示
   - 第二章：冲突引入和"金手指"暗示
   - 第三章："金手指"初现和首次"爽点"

4. "爽点"分布规划
   - 每5-10章的"爽点"安排
   - 打脸情节设计
   - 能力突破时机
   - 复仇节点规划

5. 章节概要（前20章详细，后续章节概括）

确保情节紧凑，符合"爽文"节奏要求。

请以JSON格式输出。
"""
            
            outline_content = self.llm.invoke(outline_prompt).content
            
            # 尝试解析JSON
            try:
                outline_data = json.loads(outline_content)
            except:
                outline_data = {
                    "total_chapters": target_chapters,
                    "main_plot": outline_content,
                    "volumes": [],
                    "golden_three_chapters": {},
                    "shuang_points": [],
                    "chapter_outlines": [],
                    "created_by": "架构师智能体"
                }
            
            # 保存情节大纲
            filename = "data/outline.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(outline_data, f, ensure_ascii=False, indent=2)
            
            logger.info("情节大纲创建完成")
            
            return {
                "success": True,
                "message": "情节大纲创建完成",
                "filename": filename,
                "data": outline_data
            }
            
        except Exception as e:
            logger.error(f"大纲创建失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _expand_worldbuilding(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """扩展世界观设定"""
        try:
            expansion_request = plan.get("expansion_request", "")
            
            # 读取现有世界观
            try:
                with open("data/world_bible.json", 'r', encoding='utf-8') as f:
                    world_bible = json.load(f)
            except:
                world_bible = {}
            
            expansion_prompt = f"""
基于现有世界观设定，扩展以下内容：{expansion_request}

现有世界观：
{json.dumps(world_bible, ensure_ascii=False, indent=2)}

请提供详细的扩展内容，保持与现有设定的一致性。
"""
            
            expansion_content = self.llm.invoke(expansion_prompt).content
            
            return {
                "success": True,
                "message": "世界观扩展完成",
                "expansion": expansion_content
            }
            
        except Exception as e:
            logger.error(f"世界观扩展失败: {str(e)}")
            return {"success": False, "error": str(e)}
